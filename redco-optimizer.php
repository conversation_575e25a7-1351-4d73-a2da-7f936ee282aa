<?php
/**
 * Plugin Name: Redco Optimizer
 * Plugin URI: https://redconic.com/optimizer
 * Description: A modern, scalable, and professional WordPress performance optimization plugin with modular features.
 * Version: 1.0.0
 * Author: Redconic Digital Solution
 * Author URI: https://redconic.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: redco-optimizer
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define page start time for performance tracking
if (!defined('REDCO_PAGE_START_TIME')) {
    define('REDCO_PAGE_START_TIME', microtime(true));
}

// PHP 8.1+ Compatibility - Safe function wrappers
// These functions prevent null parameter warnings by validating inputs
if (!function_exists('redco_safe_get_attached_file')) {
    /**
     * Safe wrapper for get_attached_file() to prevent null parameter warnings
     */
    function redco_safe_get_attached_file($attachment_id) {
        if (!$attachment_id || !is_numeric($attachment_id)) {
            return false;
        }

        $file_path = get_attached_file($attachment_id);

        // Ensure the result is a valid string
        if ($file_path === null || $file_path === false || $file_path === '' || !is_string($file_path)) {
            return false;
        }

        // Additional safety check for null bytes
        if (strpos($file_path, "\0") !== false) {
            return false;
        }

        return $file_path;
    }
}

if (!function_exists('redco_safe_wp_upload_dir')) {
    /**
     * Safe wrapper for wp_upload_dir() to prevent null parameter warnings
     */
    function redco_safe_wp_upload_dir() {
        $upload_dir = wp_upload_dir();

        // Ensure upload_dir is valid
        if (!$upload_dir || !is_array($upload_dir)) {
            return array(
                'basedir' => WP_CONTENT_DIR . '/uploads',
                'baseurl' => content_url('/uploads'),
                'path' => WP_CONTENT_DIR . '/uploads',
                'url' => content_url('/uploads'),
                'subdir' => '',
                'error' => false
            );
        }

        // Ensure all required keys exist and are strings
        $required_keys = array('basedir', 'baseurl', 'path', 'url');
        foreach ($required_keys as $key) {
            if (!isset($upload_dir[$key]) || $upload_dir[$key] === null || $upload_dir[$key] === false || !is_string($upload_dir[$key])) {
                $upload_dir[$key] = WP_CONTENT_DIR . '/uploads';
            }
        }

        return $upload_dir;
    }
}

if (!function_exists('redco_safe_strpos')) {
    /**
     * Safe wrapper for strpos() to prevent null parameter warnings
     */
    function redco_safe_strpos($haystack, $needle, $offset = 0) {
        if ($haystack === null || $haystack === false || !is_string($haystack)) {
            return false;
        }
        if ($needle === null || $needle === false) {
            return false;
        }
        return strpos($haystack, $needle, $offset);
    }
}

if (!function_exists('redco_safe_str_replace')) {
    /**
     * Safe wrapper for str_replace() to prevent null parameter warnings
     */
    function redco_safe_str_replace($search, $replace, $subject, &$count = null) {
        if ($subject === null || $subject === false) {
            return '';
        }
        if (!is_string($subject) && !is_array($subject)) {
            return $subject;
        }
        if ($search === null || $replace === null) {
            return $subject;
        }
        return str_replace($search, $replace, $subject, $count);
    }
}

if (!function_exists('redco_safe_basename')) {
    /**
     * Safe wrapper for basename() to prevent null parameter warnings
     */
    function redco_safe_basename($path, $suffix = '') {
        if ($path === null || $path === false || $path === '' || !is_string($path)) {
            return '';
        }
        return basename($path, $suffix);
    }
}

if (!function_exists('redco_safe_strip_tags')) {
    /**
     * Safe wrapper for strip_tags() to prevent null parameter warnings
     */
    function redco_safe_strip_tags($string, $allowed_tags = '') {
        if ($string === null || $string === false) {
            return '';
        }
        if (!is_string($string)) {
            return (string) $string;
        }
        return strip_tags($string, $allowed_tags);
    }
}

if (!function_exists('redco_safe_esc_html')) {
    /**
     * Safe wrapper for esc_html() to prevent null parameter warnings
     */
    function redco_safe_esc_html($text) {
        if ($text === null || $text === false) {
            return '';
        }
        if (!is_string($text) && !is_numeric($text)) {
            return '';
        }
        return esc_html((string) $text);
    }
}

if (!function_exists('redco_safe_dirname')) {
    /**
     * Safe wrapper for dirname() to prevent null parameter warnings
     */
    function redco_safe_dirname($path) {
        if ($path === null || $path === false || $path === '' || !is_string($path)) {
            return '';
        }
        return dirname($path);
    }
}

if (!function_exists('redco_safe_pathinfo')) {
    /**
     * Safe wrapper for pathinfo() to prevent null parameter warnings
     */
    function redco_safe_pathinfo($path, $flags = PATHINFO_ALL) {
        if ($path === null || $path === false || $path === '' || !is_string($path)) {
            return array(
                'dirname' => '',
                'basename' => '',
                'extension' => '',
                'filename' => ''
            );
        }
        return pathinfo($path, $flags);
    }
}

// Define plugin constants
define('REDCO_OPTIMIZER_VERSION', '1.1.0'); // Updated for Phase 1 enhancements
define('REDCO_OPTIMIZER_PLUGIN_FILE', __FILE__);
define('REDCO_OPTIMIZER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('REDCO_OPTIMIZER_PLUGIN_URL', plugin_dir_url(__FILE__));
define('REDCO_OPTIMIZER_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('REDCO_OPTIMIZER_PATH', REDCO_OPTIMIZER_PLUGIN_DIR); // Alias for consistency
define('REDCO_OPTIMIZER_DB_VERSION', '1.1.0'); // Database schema version

// Load core system classes
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-config.php';
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-error-handler.php';
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-settings-validator.php';
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-settings-manager.php';

require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-performance-monitor.php';
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-performance-optimizer.php';
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-database-optimizer.php';
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-advanced-cache.php';
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-api-manager.php';
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-security-manager.php';

// Load optimization classes
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-resource-loading-optimizer.php';
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-shared-optimization-settings.php';

// Load CDN provider management system
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-cdn-provider-manager.php';

// Load the main plugin class
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-loader.php';

/**
 * Initialize the plugin
 */
function redco_optimizer_init() {
    // Initialize core components
    Redco_Security_Manager::init();
    Redco_Performance_Monitor::init();

    $performance_optimizer = new Redco_Performance_Optimizer();
    $performance_optimizer->init();

    Redco_Database_Optimizer::init();
    Redco_Advanced_Cache::init();
    Redco_API_Manager::init();

    // Initialize main plugin loader
    $loader = new Redco_Optimizer_Loader();
    $loader->init();
}

// Fix admin title filter
add_filter('admin_title', 'redco_safe_admin_title', 10, 2);
function redco_safe_admin_title($admin_title, $title) {
    // Ensure both parameters are strings to prevent strip_tags() deprecation warnings
    if ($admin_title === null || $admin_title === false) {
        $admin_title = '';
    }
    if ($title === null || $title === false) {
        $title = '';
    }

    // Return the original admin_title if it's already a string
    return is_string($admin_title) ? $admin_title : (string) $admin_title;
}

// Re-enabling WP title filter for testing
add_filter('wp_title', 'redco_safe_wp_title', 10, 3);
function redco_safe_wp_title($title, $sep = '', $seplocation = '') {
    if ($title === null || $title === false) {
        return '';
    }
    return is_string($title) ? $title : (string) $title;
}

// FIXED: Admin init action with safer implementation
add_action('admin_init', 'redco_ensure_admin_page_title_safety');
function redco_ensure_admin_page_title_safety() {
    // FIXED: Only modify globals if they're actually problematic and we're in the right context
    if (!is_admin() || wp_doing_ajax()) {
        return;
    }

    // Only modify $title if it's actually null and causing issues
    global $title;
    if ($title === null) {
        $title = '';
    }

    // Don't modify other admin globals as they can interfere with WordPress admin loading
}

// Fix admin header title issues
add_action('admin_head', 'redco_fix_admin_header_title', 1);
function redco_fix_admin_header_title() {
    global $title;

    // This runs before admin-header.php line 41 where strip_tags() is called
    if ($title === null || $title === false) {
        $title = '';
    }

    // Ensure it's a string
    if (!is_string($title)) {
        $title = (string) $title;
    }
}

// Initialize plugin
add_action('plugins_loaded', 'redco_optimizer_init');

/**
 * Activation hook
 */
function redco_optimizer_activate() {
    // Set default options using centralized configuration
    $default_options = array(
        'modules_enabled' => array('wordpress-core-tweaks'),
        'version' => REDCO_OPTIMIZER_VERSION
    );

    $existing_options = get_option('redco_optimizer_options');
    if (!$existing_options) {
        add_option('redco_optimizer_options', $default_options);
    } else {
        // Ensure required modules are enabled
        if (!isset($existing_options['modules_enabled'])) {
            $existing_options['modules_enabled'] = array();
        }

        $required_modules = array('wordpress-core-tweaks');
        $updated = false;

        foreach ($required_modules as $module) {
            if (!in_array($module, $existing_options['modules_enabled'])) {
                $existing_options['modules_enabled'][] = $module;
                $updated = true;
            }
        }

        if ($updated) {
            update_option('redco_optimizer_options', $existing_options);
        }
    }

    // Set activation redirect transient (only for single site activation)
    if (!is_multisite() || !wp_is_large_network()) {
        set_transient('redco_optimizer_activation_redirect', true, 30);
    }

    // Create necessary database tables if needed
    do_action('redco_optimizer_activate');
}
// Re-enabled for Phase 1 enhancements
register_activation_hook(__FILE__, 'redco_optimizer_activate');

/**
 * Deactivation hook
 */
function redco_optimizer_deactivate() {
    // Clean up temporary data
    do_action('redco_optimizer_deactivate');
}
// Re-enabled for Phase 1 enhancements
register_deactivation_hook(__FILE__, 'redco_optimizer_deactivate');

// Hook for database upgrades
add_action('plugins_loaded', 'redco_optimizer_check_db_version');

/**
 * Check database version and upgrade if needed
 */
function redco_optimizer_check_db_version() {
    $current_db_version = get_option('redco_optimizer_db_version', '1.0.0');

    if (version_compare($current_db_version, REDCO_OPTIMIZER_DB_VERSION, '<')) {
        redco_optimizer_upgrade_database($current_db_version);
    }
}

/**
 * Upgrade database schema
 */
function redco_optimizer_upgrade_database($from_version) {
    global $wpdb;

    // Prevent multiple simultaneous upgrades
    $upgrade_lock = get_transient('redco_optimizer_upgrading');
    if ($upgrade_lock) {
        return;
    }

    set_transient('redco_optimizer_upgrading', true, 300); // 5 minute lock

    try {
        // Phase 1 enhancements - Add new tables for scheduling and enhanced features
        if (version_compare($from_version, '1.1.0', '<')) {
            redco_optimizer_create_phase1_tables();
        }

        // Update database version
        update_option('redco_optimizer_db_version', REDCO_OPTIMIZER_DB_VERSION);

        // Clear upgrade lock
        delete_transient('redco_optimizer_upgrading');

    } catch (Exception $e) {
        // Log error and clear lock
        error_log('Redco Optimizer database upgrade failed: ' . $e->getMessage());
        delete_transient('redco_optimizer_upgrading');
    }
}

/**
 * Create Phase 1 enhancement tables
 */
function redco_optimizer_create_phase1_tables() {
    global $wpdb;

    $charset_collate = $wpdb->get_charset_collate();


}

/**
 * Uninstall hook
 */
function redco_optimizer_uninstall() {
    // Only run if user explicitly chose to remove data
    if (get_option('redco_webp_remove_data_on_uninstall', false)) {
        do_action('redco_optimizer_uninstall');
    }
}
// Register uninstall hook
register_uninstall_hook(__FILE__, 'redco_optimizer_uninstall');

/**
 * Show deactivation notice for WebP restoration
 */
function redco_optimizer_deactivation_notice() {
    if (!get_transient('redco_webp_deactivation_notice')) {
        return;
    }

    // Check if WebP module has converted images
    global $wpdb;
    $converted_count = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->postmeta}
        WHERE meta_key = '_webp_conversion_data'
    ");

    if ($converted_count > 0) {
        // Show notice about WebP restoration
        add_action('admin_notices', function() use ($converted_count) {
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p><strong>RedCo Optimizer:</strong> ' . sprintf(
                __('You have %d images converted to WebP. Consider running the restoration tool before deactivating.', 'redco-optimizer'),
                $converted_count
            ) . '</p>';
            echo '</div>';
        });
    }
}
// TEMPORARILY DISABLED: Admin notice causing crashes
// add_action('admin_notices', 'redco_optimizer_deactivation_notice');

/**
 * AJAX handler for dismissing WebP notice
 */
function redco_dismiss_webp_notice() {
    if (!wp_verify_nonce($_POST['nonce'], 'redco_dismiss_webp_notice')) {
        wp_die('Security check failed');
    }

    delete_transient('redco_webp_deactivation_notice');
    wp_send_json_success();
}
// TEMPORARILY DISABLED: AJAX handlers causing issues
// add_action('wp_ajax_redco_dismiss_webp_notice', 'redco_dismiss_webp_notice');

/**
 * AJAX handler for setting remove data on uninstall
 */
function redco_set_remove_data_on_uninstall() {
    if (!wp_verify_nonce($_POST['nonce'], 'redco_set_remove_data_on_uninstall')) {
        wp_die('Security check failed');
    }

    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    update_option('redco_webp_remove_data_on_uninstall', true);
    delete_transient('redco_webp_deactivation_notice');
    wp_send_json_success();
}
// TEMPORARILY DISABLED: AJAX handlers causing issues
// add_action('wp_ajax_redco_set_remove_data_on_uninstall', 'redco_set_remove_data_on_uninstall');
