<?php
/**
 * Test Enhanced Optimize Button Functionality
 * 
 * This file tests the comprehensive enhancements made to the Optimize button
 * in the Redco Optimizer plugin header.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Enhanced_Optimize_Button_Test {
    
    /**
     * Run all tests
     */
    public static function run_tests() {
        echo "<h2>Redco Optimizer - Enhanced Optimize Button Test</h2>\n";
        
        $tests = array(
            'test_button_html_structure',
            'test_css_styling_enhancements',
            'test_javascript_functionality',
            'test_ajax_handlers',
            'test_pre_optimization_analysis',
            'test_quick_optimization_functionality',
            'test_user_experience_improvements',
            'test_performance_impact_estimation'
        );
        
        $passed = 0;
        $total = count($tests);
        
        foreach ($tests as $test) {
            echo "<h3>Running: " . str_replace('_', ' ', ucfirst($test)) . "</h3>\n";
            
            try {
                $result = self::$test();
                if ($result) {
                    echo "<p style='color: green;'>✓ PASSED</p>\n";
                    $passed++;
                } else {
                    echo "<p style='color: red;'>✗ FAILED</p>\n";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>✗ ERROR: " . $e->getMessage() . "</p>\n";
            }
        }
        
        echo "<h3>Test Results: {$passed}/{$total} tests passed</h3>\n";
        
        if ($passed === $total) {
            echo "<p style='color: green; font-weight: bold;'>🎉 All tests passed! The enhanced Optimize button is ready for production.</p>\n";
        } else {
            echo "<p style='color: red; font-weight: bold;'>⚠️ Some tests failed. Please review the issues above.</p>\n";
        }
    }

    /**
     * Test button HTML structure enhancements
     */
    private static function test_button_html_structure() {
        // Check if Admin UI class exists
        if (!class_exists('Redco_Optimizer_Admin_UI')) {
            echo "<p>Redco_Optimizer_Admin_UI class not found</p>\n";
            return false;
        }

        // Test enhanced button structure
        $admin_ui = new Redco_Optimizer_Admin_UI();
        
        // Capture output of dashboard rendering
        ob_start();
        $admin_ui->render_dashboard();
        $output = ob_get_clean();

        // Check for enhanced button elements
        $required_elements = array(
            'enhanced-optimize-btn',
            'data-optimization-level="comprehensive"',
            'dashicons-superhero',
            'optimize-text',
            'optimize-badge',
            'quick-optimize-btn',
            'data-action="quick_optimization"'
        );

        foreach ($required_elements as $element) {
            if (strpos($output, $element) === false) {
                echo "<p>Missing element: {$element}</p>\n";
                return false;
            }
        }

        echo "<p>✓ Enhanced button HTML structure is correct</p>\n";
        return true;
    }

    /**
     * Test CSS styling enhancements
     */
    private static function test_css_styling_enhancements() {
        $css_file = REDCO_OPTIMIZER_PATH . 'assets/css/module-layout-standard.css';
        
        if (!file_exists($css_file)) {
            echo "<p>CSS file not found: {$css_file}</p>\n";
            return false;
        }

        $css_content = file_get_contents($css_file);

        // Check for enhanced styling elements
        $required_styles = array(
            '.enhanced-optimize-btn',
            'linear-gradient(135deg, #ff9800',
            '.optimize-badge',
            '.quick-optimize-btn',
            'optimizePulse',
            'box-shadow:'
        );

        foreach ($required_styles as $style) {
            if (strpos($css_content, $style) === false) {
                echo "<p>Missing CSS style: {$style}</p>\n";
                return false;
            }
        }

        echo "<p>✓ Enhanced CSS styling is present</p>\n";
        return true;
    }

    /**
     * Test JavaScript functionality enhancements
     */
    private static function test_javascript_functionality() {
        $js_file = REDCO_OPTIMIZER_PATH . 'assets/js/admin-scripts.js';
        
        if (!file_exists($js_file)) {
            echo "<p>JavaScript file not found: {$js_file}</p>\n";
            return false;
        }

        $js_content = file_get_contents($js_file);

        // Check for enhanced JavaScript functions
        $required_functions = array(
            'performPreOptimizationAnalysis',
            'openEnhancedPerformanceAuditWizard',
            'determineOptimalWizardFlow',
            'quick-optimize-btn',
            'redco_apply_quick_optimizations'
        );

        foreach ($required_functions as $function) {
            if (strpos($js_content, $function) === false) {
                echo "<p>Missing JavaScript function/element: {$function}</p>\n";
                return false;
            }
        }

        echo "<p>✓ Enhanced JavaScript functionality is present</p>\n";
        return true;
    }

    /**
     * Test AJAX handlers registration
     */
    private static function test_ajax_handlers() {
        global $wp_filter;
        
        $required_actions = array(
            'wp_ajax_redco_pre_optimization_analysis',
            'wp_ajax_redco_apply_quick_optimizations'
        );

        foreach ($required_actions as $action) {
            if (!isset($wp_filter[$action])) {
                echo "<p>AJAX action not registered: {$action}</p>\n";
                return false;
            }
        }

        echo "<p>✓ Enhanced AJAX handlers are registered</p>\n";
        return true;
    }

    /**
     * Test pre-optimization analysis functionality
     */
    private static function test_pre_optimization_analysis() {
        if (!class_exists('Redco_Optimizer_Admin_UI')) {
            echo "<p>Redco_Optimizer_Admin_UI class not found</p>\n";
            return false;
        }

        $reflection = new ReflectionClass('Redco_Optimizer_Admin_UI');
        
        $required_methods = array(
            'ajax_pre_optimization_analysis',
            'perform_comprehensive_analysis',
            'calculate_optimization_effectiveness',
            'identify_quick_wins',
            'generate_optimization_recommendations',
            'estimate_performance_improvements'
        );

        foreach ($required_methods as $method) {
            if (!$reflection->hasMethod($method)) {
                echo "<p>Missing method: {$method}</p>\n";
                return false;
            }
        }

        echo "<p>✓ Pre-optimization analysis methods exist</p>\n";
        return true;
    }

    /**
     * Test quick optimization functionality
     */
    private static function test_quick_optimization_functionality() {
        if (!class_exists('Redco_Optimizer_Admin_UI')) {
            echo "<p>Redco_Optimizer_Admin_UI class not found</p>\n";
            return false;
        }

        $reflection = new ReflectionClass('Redco_Optimizer_Admin_UI');
        
        $required_methods = array(
            'ajax_apply_quick_optimizations',
            'apply_quick_optimizations'
        );

        foreach ($required_methods as $method) {
            if (!$reflection->hasMethod($method)) {
                echo "<p>Missing method: {$method}</p>\n";
                return false;
            }
        }

        echo "<p>✓ Quick optimization methods exist</p>\n";
        return true;
    }

    /**
     * Test user experience improvements
     */
    private static function test_user_experience_improvements() {
        // Test tooltip enhancements
        $admin_ui = new Redco_Optimizer_Admin_UI();
        
        ob_start();
        $admin_ui->render_dashboard();
        $output = ob_get_clean();

        // Check for UX improvements
        $ux_elements = array(
            'AI-powered optimization wizard',
            'Apply essential optimizations instantly',
            'data-tooltip=',
            'optimize-badge'
        );

        foreach ($ux_elements as $element) {
            if (strpos($output, $element) === false) {
                echo "<p>Missing UX element: {$element}</p>\n";
                return false;
            }
        }

        echo "<p>✓ User experience improvements are present</p>\n";
        return true;
    }

    /**
     * Test performance impact estimation
     */
    private static function test_performance_impact_estimation() {
        if (!class_exists('Redco_Optimizer_Admin_UI')) {
            echo "<p>Redco_Optimizer_Admin_UI class not found</p>\n";
            return false;
        }

        // Test that performance estimation methods exist
        $reflection = new ReflectionClass('Redco_Optimizer_Admin_UI');
        
        if (!$reflection->hasMethod('estimate_performance_improvements')) {
            echo "<p>Performance estimation method missing</p>\n";
            return false;
        }

        // Test that the method includes proper calculations
        $method = $reflection->getMethod('estimate_performance_improvements');
        $method->setAccessible(true);

        echo "<p>✓ Performance impact estimation functionality exists</p>\n";
        return true;
    }
}

// Run tests if this file is accessed directly in admin context
if (is_admin() && current_user_can('manage_options')) {
    add_action('admin_init', function() {
        if (isset($_GET['run_optimize_button_test']) && $_GET['run_optimize_button_test'] === '1') {
            Redco_Enhanced_Optimize_Button_Test::run_tests();
            exit;
        }
    });
}
