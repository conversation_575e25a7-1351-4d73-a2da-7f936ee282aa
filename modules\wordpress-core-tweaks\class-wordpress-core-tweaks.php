<?php
/**
 * WordPress Core Tweaks Module for Redco Optimizer
 *
 * Unified module combining emoji removal, version string removal, and autosave optimization.
 * Consolidates: Emoji Stripper, Query String Remover, Asset Version Remover, Autosave Reducer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_WordPress_Core_Tweaks {

    /**
     * Module settings
     */
    private $settings = array();

    /**
     * Singleton instance
     */
    private static $instance = null;

    /**
     * AJAX handlers registered flag
     */
    private static $ajax_handlers_registered = false;

    /**
     * Initialization flag
     */
    private $initialized = false;

    /**
     * Settings loaded flag to prevent recursion
     */
    private $settings_loaded = false;

    /**
     * Memory guard to prevent excessive instantiation
     */
    private static $memory_guard = false;

    /**
     * Constructor
     */
    public function __construct() {
        // Memory guard to prevent excessive instantiation
        if (self::$memory_guard) {
            return;
        }

        // Prevent multiple instances from causing issues
        if (self::$instance !== null) {
            return;
        }

        self::$memory_guard = true;
        self::$instance = $this;

        // Register AJAX handlers only once
        if (is_admin() && !self::$ajax_handlers_registered) {
            add_action('wp_ajax_redco_apply_recommended_tweaks', array($this, 'ajax_apply_recommended_tweaks'));
            add_action('wp_ajax_redco_reset_all_tweaks', array($this, 'ajax_reset_all_tweaks'));

            // CRITICAL FIX: Add integration with global settings save system
            add_filter('redco_optimizer_sanitize_wordpress_core_tweaks_settings', array($this, 'sanitize_settings_for_global_system'), 10, 2);

            // CRITICAL FIX: Ensure module is properly registered with Settings Manager
            add_action('init', array($this, 'register_with_settings_manager'), 5);

            self::$ajax_handlers_registered = true;
        }

        // Defer initialization to avoid circular dependencies
        add_action('wp_loaded', array($this, 'deferred_init'), 1);
    }

    /**
     * Deferred initialization to avoid circular dependencies
     */
    public function deferred_init() {
        // Only initialize once and if not already initialized
        if ($this->initialized) {
            return;
        }

        // Check if module is enabled without causing recursion
        if ($this->is_module_enabled_safe() && !$this->initialized) {
            $this->init();
            $this->initialized = true;
        }
    }

    /**
     * Safe module enabled check to prevent recursion
     */
    private function is_module_enabled_safe() {
        // Direct database check to avoid function call recursion
        $options = get_option('redco_optimizer_options', array());
        $enabled_modules = isset($options['modules_enabled']) ? $options['modules_enabled'] : array();
        return is_array($enabled_modules) && in_array('wordpress-core-tweaks', $enabled_modules);
    }

    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize the module
     */
    private function init() {
        // Load settings
        $this->load_settings();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Load module settings
     */
    private function load_settings() {
        // Use the safe get_current_settings method to avoid recursion
        $this->settings = $this->get_current_settings();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Emoji removal hooks
        $this->init_emoji_hooks();

        // Version removal hooks
        $this->init_version_removal_hooks();

        // Query string removal hooks
        $this->init_query_string_removal_hooks();

        // Autosave optimization hooks
        $this->init_autosave_hooks();

        // Note: AJAX handlers are now registered in constructor for admin functionality
    }

    /**
     * Initialize emoji removal hooks
     */
    private function init_emoji_hooks() {
        // Remove emoji scripts and styles from frontend
        if ($this->settings['emoji_remove_frontend'] && !is_admin()) {
            $this->remove_emoji_hooks();
        }

        // Remove emoji scripts and styles from admin
        if ($this->settings['emoji_remove_admin'] && is_admin()) {
            $this->remove_emoji_hooks();
        }

        // Remove emoji from feeds
        if ($this->settings['emoji_remove_feeds']) {
            remove_filter('the_content_feed', 'wp_staticize_emoji');
            remove_filter('the_excerpt_rss', 'wp_staticize_emoji');
            remove_filter('wp_mail', 'wp_staticize_emoji_for_email');
        }

        // Remove emoji from emails
        if ($this->settings['emoji_remove_emails']) {
            remove_filter('wp_mail', 'wp_staticize_emoji_for_email');
        }
    }

    /**
     * Remove emoji-related hooks
     */
    private function remove_emoji_hooks() {
        // Remove emoji detection script
        remove_action('wp_head', 'print_emoji_detection_script', 7);
        remove_action('admin_print_scripts', 'print_emoji_detection_script');

        // Remove emoji styles
        remove_action('wp_print_styles', 'print_emoji_styles');
        remove_action('admin_print_styles', 'print_emoji_styles');

        // Remove emoji from content
        remove_filter('the_content_feed', 'wp_staticize_emoji');
        remove_filter('the_excerpt_rss', 'wp_staticize_emoji');
        remove_filter('wp_mail', 'wp_staticize_emoji_for_email');

        // Remove emoji DNS prefetch
        add_filter('wp_resource_hints', array($this, 'remove_emoji_dns_prefetch'), 10, 2);

        // Remove TinyMCE emoji plugin
        add_filter('tiny_mce_plugins', array($this, 'remove_tinymce_emoji'));
    }

    /**
     * Remove emoji DNS prefetch
     */
    public function remove_emoji_dns_prefetch($urls, $relation_type) {
        if ('dns-prefetch' === $relation_type) {
            $emoji_svg_url = apply_filters('emoji_svg_url', 'https://s.w.org/images/core/emoji/');
            $urls = array_diff($urls, array($emoji_svg_url));
        }

        return $urls;
    }

    /**
     * Remove TinyMCE emoji plugin
     */
    public function remove_tinymce_emoji($plugins) {
        if (is_array($plugins)) {
            return array_diff($plugins, array('wpemoji'));
        }

        return array();
    }

    /**
     * Initialize version removal hooks
     */
    private function init_version_removal_hooks() {
        // Don't remove versions in admin
        if (is_admin()) {
            return;
        }

        // Add universal null safety filters with highest priority
        add_filter('style_loader_src', array($this, 'ensure_valid_src'), 5, 2);
        add_filter('script_loader_src', array($this, 'ensure_valid_src'), 5, 2);

        // Remove versions from CSS files
        if ($this->settings['remove_css_versions']) {
            add_filter('style_loader_src', array($this, 'remove_asset_version'), 10, 2);
        }

        // Remove versions from JS files
        if ($this->settings['remove_js_versions']) {
            add_filter('script_loader_src', array($this, 'remove_asset_version'), 10, 2);
        }

        // Remove WordPress version from generator tag
        if ($this->settings['remove_wp_version']) {
            remove_action('wp_head', 'wp_generator');
            add_filter('the_generator', '__return_empty_string');
        }
    }

    /**
     * Ensure src is always a valid string to prevent WordPress core ltrim() errors
     */
    public function ensure_valid_src($src, $handle = '') {
        // Convert null or non-string values to empty string
        if (!is_string($src)) {
            return '';
        }

        // Return the valid string as-is
        return $src;
    }

    /**
     * Remove version from asset URLs (combines both query string and asset version removal)
     */
    public function remove_asset_version($src, $handle = '') {
        // Ensure src is not null and is a string - return empty string if invalid
        if (!is_string($src)) {
            return '';
        }

        // Return empty string for empty src to prevent WordPress core issues
        if (empty($src)) {
            return '';
        }

        // Safely handle exclude_handles - convert string to array if needed
        $exclude_handles = $this->settings['exclude_handles'];
        if (is_string($exclude_handles)) {
            $exclude_handles = !empty($exclude_handles) ? explode("\n", $exclude_handles) : array();
        } elseif (!is_array($exclude_handles)) {
            $exclude_handles = array();
        }

        // Skip if handle is excluded
        if (!empty($handle) && in_array($handle, $exclude_handles)) {
            return $src;
        }

        // Skip external files unless specifically allowed
        if (!$this->should_process_url($src)) {
            return $src;
        }

        // Remove all query parameters including version strings
        $parsed_url = parse_url($src);
        if (isset($parsed_url['query'])) {
            $src = str_replace('?' . $parsed_url['query'], '', $src);
        }

        return $src;
    }

    /**
     * Check if URL should be processed for version removal
     */
    private function should_process_url($src) {
        // Ensure src is not null and is a string
        if (!is_string($src) || empty($src)) {
            return false;
        }

        $home_url = home_url();
        $wp_content_url = content_url();
        $wp_includes_url = includes_url();

        // Always process local files
        if (strpos($src, $home_url) === 0) {
            return true;
        }

        // Process WordPress core files
        if (strpos($src, $wp_includes_url) === 0) {
            return true;
        }

        // Process theme files if enabled
        if ($this->settings['remove_theme_versions'] && strpos($src, get_template_directory_uri()) === 0) {
            return true;
        }

        // Process child theme files if enabled
        if ($this->settings['remove_theme_versions'] && get_stylesheet_directory_uri() !== get_template_directory_uri()) {
            if (strpos($src, get_stylesheet_directory_uri()) === 0) {
                return true;
            }
        }

        // Process plugin files if enabled
        if ($this->settings['remove_plugin_versions'] && strpos($src, plugins_url()) === 0) {
            return true;
        }

        return false;
    }

    /**
     * Initialize query string removal hooks
     */
    private function init_query_string_removal_hooks() {
        // Don't remove query strings in admin
        if (is_admin()) {
            return;
        }

        // Add universal null safety filters with highest priority (if not already added)
        if (!has_filter('style_loader_src', array($this, 'ensure_valid_src'))) {
            add_filter('style_loader_src', array($this, 'ensure_valid_src'), 5, 2);
        }
        if (!has_filter('script_loader_src', array($this, 'ensure_valid_src'))) {
            add_filter('script_loader_src', array($this, 'ensure_valid_src'), 5, 2);
        }

        // Remove query strings from CSS files
        if ($this->settings['remove_css_query_strings']) {
            add_filter('style_loader_src', array($this, 'remove_query_strings'), 15, 2);
        }

        // Remove query strings from JS files
        if ($this->settings['remove_js_query_strings']) {
            add_filter('script_loader_src', array($this, 'remove_query_strings'), 15, 2);
        }
    }

    /**
     * Remove query strings from asset URLs (more aggressive than version removal)
     */
    public function remove_query_strings($src, $handle = '') {
        // Ensure src is not null and is a string - return empty string if invalid
        if (!is_string($src)) {
            return '';
        }

        // Return empty string for empty src to prevent WordPress core issues
        if (empty($src)) {
            return '';
        }

        // Safely handle query_string_exclude_handles - convert string to array if needed
        $query_exclude_handles = $this->settings['query_string_exclude_handles'];
        if (is_string($query_exclude_handles)) {
            $query_exclude_handles = !empty($query_exclude_handles) ? explode("\n", $query_exclude_handles) : array();
        } elseif (!is_array($query_exclude_handles)) {
            $query_exclude_handles = array();
        }

        // Skip if handle is excluded
        if (!empty($handle) && in_array($handle, $query_exclude_handles)) {
            return $src;
        }

        // Skip external files unless specifically allowed
        if (!$this->should_process_query_string_url($src)) {
            return $src;
        }

        // Remove all query parameters if aggressive mode is enabled
        if ($this->settings['remove_all_query_params']) {
            $parsed_url = parse_url($src);
            if (isset($parsed_url['query'])) {
                $src = str_replace('?' . $parsed_url['query'], '', $src);
            }
        } else {
            // Remove only common query parameters (more conservative)
            $src = preg_replace('/\?.*$/', '', $src);
        }

        return $src;
    }

    /**
     * Check if URL should be processed for query string removal
     */
    private function should_process_query_string_url($src) {
        // Ensure src is not null before using strpos
        $src = (string) $src;
        if (empty($src)) {
            return false;
        }

        $home_url = home_url();
        $wp_content_url = content_url();
        $wp_includes_url = includes_url();

        // Always process local files
        if (strpos($src, $home_url) === 0) {
            return true;
        }

        // Process WordPress core files
        if (strpos($src, $wp_includes_url) === 0) {
            return true;
        }

        // Process content files (themes, plugins, uploads)
        if (strpos($src, $wp_content_url) === 0) {
            return true;
        }

        return false;
    }

    /**
     * Initialize autosave optimization hooks
     */
    private function init_autosave_hooks() {
        // Only apply in admin area
        if (!is_admin()) {
            return;
        }

        // Disable autosave completely if setting is enabled
        if ($this->settings['disable_autosave']) {
            add_action('wp_print_scripts', array($this, 'disable_autosave_script'));
        } else {
            // Modify autosave interval
            add_action('wp_print_scripts', array($this, 'modify_autosave_interval'));
        }

        // Limit post revisions if enabled
        if ($this->settings['limit_revisions']) {
            add_filter('wp_revisions_to_keep', array($this, 'limit_post_revisions'), 10, 2);
        }
    }

    /**
     * Disable autosave script completely
     */
    public function disable_autosave_script() {
        global $pagenow;

        // Only on post edit pages
        if (!in_array($pagenow, array('post.php', 'post-new.php'))) {
            return;
        }

        // Check if current post type is in allowed list
        if (!$this->is_allowed_post_type()) {
            return;
        }

        wp_deregister_script('autosave');
    }

    /**
     * Modify autosave interval
     */
    public function modify_autosave_interval() {
        global $pagenow;

        // Only on post edit pages
        if (!in_array($pagenow, array('post.php', 'post-new.php'))) {
            return;
        }

        // Check if current post type is in allowed list
        if (!$this->is_allowed_post_type()) {
            return;
        }

        $interval = $this->settings['autosave_interval'];

        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Override autosave interval
            if (typeof wp !== 'undefined' && wp.autosave) {
                wp.autosave.server.tempBlockSave = function() {
                    return false;
                };

                // Set new interval
                if (wp.autosave.server.triggerSave) {
                    clearInterval(wp.autosave.server.triggerSave);
                    wp.autosave.server.triggerSave = setInterval(function() {
                        wp.autosave.server.triggerSave();
                    }, <?php echo intval($interval * 1000); ?>);
                }
            }

            // Also modify heartbeat settings for autosave
            if (typeof wp !== 'undefined' && wp.heartbeat) {
                wp.heartbeat.interval(<?php echo intval($interval); ?>);
            }
        });
        </script>
        <?php
    }

    /**
     * Check if current post type is allowed for autosave modifications
     */
    private function is_allowed_post_type() {
        global $post, $pagenow;

        $post_type = '';

        if ($pagenow === 'post-new.php' && isset($_GET['post_type'])) {
            $post_type = sanitize_text_field($_GET['post_type']);
        } elseif ($pagenow === 'post.php' && isset($post) && $post->post_type) {
            $post_type = $post->post_type;
        } elseif (isset($_GET['post']) && is_numeric($_GET['post'])) {
            $post_obj = get_post(intval($_GET['post']));
            if ($post_obj) {
                $post_type = $post_obj->post_type;
            }
        }

        // Safely handle autosave_post_types - ensure it's an array
        $autosave_post_types = $this->settings['autosave_post_types'];
        if (!is_array($autosave_post_types)) {
            $autosave_post_types = array('post', 'page'); // Default fallback
        }

        return in_array($post_type, $autosave_post_types);
    }

    /**
     * Limit post revisions
     */
    public function limit_post_revisions($num, $post) {
        // Safely handle autosave_post_types - ensure it's an array
        $autosave_post_types = $this->settings['autosave_post_types'];
        if (!is_array($autosave_post_types)) {
            $autosave_post_types = array('post', 'page'); // Default fallback
        }

        // Check if post type is in allowed list
        if (!in_array($post->post_type, $autosave_post_types)) {
            return $num;
        }

        return $this->settings['max_revisions'];
    }

    /**
     * Get comprehensive statistics for all features
     */
    public function get_stats() {
        $wp_default_autosave = 60; // WordPress default autosave interval
        $current_interval = $this->settings['disable_autosave'] ? 0 : $this->settings['autosave_interval'];

        // Safely handle exclude_handles - convert string to array if needed
        $exclude_handles = $this->settings['exclude_handles'];
        if (is_string($exclude_handles)) {
            $exclude_handles = !empty($exclude_handles) ? explode("\n", $exclude_handles) : array();
        } elseif (!is_array($exclude_handles)) {
            $exclude_handles = array();
        }

        // Safely handle query_string_exclude_handles - convert string to array if needed
        $query_exclude_handles = $this->settings['query_string_exclude_handles'];
        if (is_string($query_exclude_handles)) {
            $query_exclude_handles = !empty($query_exclude_handles) ? explode("\n", $query_exclude_handles) : array();
        } elseif (!is_array($query_exclude_handles)) {
            $query_exclude_handles = array();
        }

        $stats = array(
            // Emoji removal stats
            'emoji_frontend_removed' => $this->settings['emoji_remove_frontend'],
            'emoji_admin_removed' => $this->settings['emoji_remove_admin'],
            'emoji_feeds_removed' => $this->settings['emoji_remove_feeds'],
            'emoji_emails_removed' => $this->settings['emoji_remove_emails'],
            'emoji_estimated_savings' => '15-20 KB per page',

            // Version removal stats
            'css_versions_removed' => $this->settings['remove_css_versions'],
            'js_versions_removed' => $this->settings['remove_js_versions'],
            'theme_versions_removed' => $this->settings['remove_theme_versions'],
            'plugin_versions_removed' => $this->settings['remove_plugin_versions'],
            'wp_version_removed' => $this->settings['remove_wp_version'],
            'excluded_handles' => count($exclude_handles),

            // Query string removal stats
            'css_query_strings_removed' => $this->settings['remove_css_query_strings'],
            'js_query_strings_removed' => $this->settings['remove_js_query_strings'],
            'aggressive_query_removal' => $this->settings['remove_all_query_params'],
            'query_excluded_handles' => count($query_exclude_handles),

            // Autosave optimization stats
            'autosave_disabled' => $this->settings['disable_autosave'],
            'current_autosave_interval' => $current_interval,
            'wp_default_autosave_interval' => $wp_default_autosave,
            'autosave_reduction_percentage' => $this->settings['disable_autosave'] ? 100 :
                round((($current_interval - $wp_default_autosave) / $wp_default_autosave) * 100, 1),
            'affected_post_types' => $this->settings['autosave_post_types'],
            'revisions_limited' => $this->settings['limit_revisions'],
            'max_revisions' => $this->settings['max_revisions']
        );

        return $stats;
    }

    /**
     * Enhanced AJAX handler for applying recommended tweaks
     */
    public function ajax_apply_recommended_tweaks() {
        // Enhanced security check
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce') || !current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => 'Security check failed. Please refresh the page and try again.',
                'code' => 'SECURITY_FAILED'
            ));
            return;
        }

        try {
            // Get intelligent recommendations based on current site state
            $recommended_settings = $this->get_intelligent_recommendations();

            // Allow custom settings override from frontend
            if (isset($_POST['settings']) && is_array($_POST['settings'])) {
                $custom_settings = $_POST['settings'];
                $recommended_settings = array_merge($recommended_settings, $custom_settings);
            }

            // Enhanced setting mapping with validation
            $setting_map = array(
                'disable_emojis' => 'emoji_remove_frontend',
                'remove_version_numbers' => 'remove_wp_version',
                'disable_xmlrpc' => 'disable_xmlrpc',
                'limit_post_revisions' => 'limit_revisions',
                'disable_pingbacks' => 'disable_pingbacks',
                'remove_shortlink' => 'remove_shortlink',
                'remove_css_versions' => 'remove_css_versions',
                'remove_js_versions' => 'remove_js_versions',
                'emoji_remove_feeds' => 'emoji_remove_feeds',
                'emoji_remove_emails' => 'emoji_remove_emails'
            );

            // Get current settings with error handling
            $current_settings = $this->get_current_settings();
            if (!is_array($current_settings)) {
                $current_settings = $this->get_default_settings();
            }

            // Track applied changes for detailed feedback
            $applied_changes = array();
            $skipped_changes = array();
            $performance_impact = 0;

            // Apply recommended settings with validation
            foreach ($recommended_settings as $js_key => $value) {
                if (isset($setting_map[$js_key])) {
                    $setting_key = $setting_map[$js_key];
                    $old_value = isset($current_settings[$setting_key]) ? $current_settings[$setting_key] : false;
                    $new_value = (bool) $value;

                    // Only apply if value is actually changing
                    if ($old_value !== $new_value) {
                        $current_settings[$setting_key] = $new_value;
                        $applied_changes[$setting_key] = array(
                            'old' => $old_value,
                            'new' => $new_value,
                            'impact' => $this->get_setting_performance_impact($setting_key, $new_value)
                        );
                        $performance_impact += $applied_changes[$setting_key]['impact'];
                    } else {
                        $skipped_changes[$setting_key] = 'Already optimized';
                    }
                } else {
                    error_log("Redco: Unknown setting key in recommendations: {$js_key}");
                }
            }

            // Validate settings before saving
            $validated_settings = $this->validate_settings($current_settings);
            if (!$validated_settings) {
                wp_send_json_error(array(
                    'message' => 'Settings validation failed. Please check your configuration.',
                    'code' => 'VALIDATION_FAILED'
                ));
                return;
            }

            // Create backup before applying changes
            $backup_created = $this->create_settings_backup();

            // Save settings with error handling
            $option_name = 'redco_optimizer_wordpress_core_tweaks';
            $save_result = update_option($option_name, $validated_settings);

            if (!$save_result) {
                wp_send_json_error(array(
                    'message' => 'Failed to save settings. Please try again.',
                    'code' => 'SAVE_FAILED'
                ));
                return;
            }

            // Clear relevant caches with enhanced cache clearing
            $this->clear_optimization_caches();

            // Generate performance report
            $performance_report = $this->generate_performance_report($applied_changes, $performance_impact);

            // Prepare detailed response
            $response_data = array(
                'message' => $this->get_success_message(count($applied_changes)),
                'applied_changes' => $applied_changes,
                'skipped_changes' => $skipped_changes,
                'performance_report' => $performance_report,
                'backup_created' => $backup_created,
                'total_optimizations' => count($applied_changes),
                'estimated_improvement' => $this->format_performance_improvement($performance_impact),
                'next_steps' => $this->get_next_step_recommendations(),
                'code' => 'SUCCESS'
            );

            wp_send_json_success($response_data);

        } catch (Exception $e) {
            error_log("Redco WordPress Core Tweaks Apply Recommended Error: " . $e->getMessage());
            wp_send_json_error(array(
                'message' => 'An unexpected error occurred. Please try again or contact support.',
                'code' => 'UNEXPECTED_ERROR',
                'debug_info' => WP_DEBUG ? $e->getMessage() : null
            ));
        }
    }

    /**
     * AJAX handler for resetting all tweaks
     */
    public function ajax_reset_all_tweaks() {
        // Security check
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce') || !current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Security check failed'));
            return;
        }

        // Reset to default settings
        $default_settings = $this->get_default_settings();
        $option_name = 'redco_optimizer_wordpress_core_tweaks';
        update_option($option_name, $default_settings);

        // Clear any relevant caches
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        wp_send_json_success(array(
            'message' => 'All tweaks have been reset to default settings!',
            'reset_settings' => $default_settings
        ));
    }

    /**
     * Sanitize settings for global system integration
     */
    public function sanitize_settings_for_global_system($sanitized, $raw_settings) {
        // Ensure this is an array
        if (!is_array($sanitized)) {
            $sanitized = array();
        }

        // Get defaults to ensure all keys exist
        $defaults = $this->get_default_settings();
        $sanitized = array_merge($defaults, $sanitized);

        // Sanitize boolean values
        $boolean_fields = array(
            'emoji_remove_frontend', 'emoji_remove_admin', 'emoji_remove_feeds', 'emoji_remove_emails',
            'remove_wp_version', 'remove_css_versions', 'remove_js_versions', 'remove_theme_versions', 'remove_plugin_versions',
            'remove_css_query_strings', 'remove_js_query_strings', 'remove_all_query_params',
            'disable_autosave', 'limit_revisions', 'disable_xmlrpc', 'disable_pingbacks', 'remove_shortlink'
        );

        foreach ($boolean_fields as $field) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = (bool) $sanitized[$field];
            }
        }

        // Sanitize numeric values
        if (isset($sanitized['autosave_interval'])) {
            $sanitized['autosave_interval'] = max(30, min(3600, intval($sanitized['autosave_interval'])));
        }

        if (isset($sanitized['max_revisions'])) {
            $sanitized['max_revisions'] = max(1, min(50, intval($sanitized['max_revisions'])));
        }

        // Sanitize array values
        $array_fields = array('autosave_post_types', 'exclude_handles', 'query_string_exclude_handles');
        foreach ($array_fields as $field) {
            if (isset($sanitized[$field])) {
                if (is_string($sanitized[$field])) {
                    // Convert textarea input to array
                    $sanitized[$field] = !empty($sanitized[$field])
                        ? array_filter(array_map('trim', explode("\n", $sanitized[$field])))
                        : array();
                } elseif (!is_array($sanitized[$field])) {
                    $sanitized[$field] = $defaults[$field];
                }
            }
        }

        return $sanitized;
    }

    /**
     * Register module with Settings Manager for proper integration
     */
    public function register_with_settings_manager() {
        // Ensure Settings Manager exists
        if (!class_exists('Redco_Settings_Manager')) {
            return;
        }

        // Register the module with its option name
        $option_name = 'redco_optimizer_wordpress_core_tweaks';

        // Ensure the module is properly registered
        add_filter('redco_optimizer_get_module_option_name_wordpress_core_tweaks', function() use ($option_name) {
            return $option_name;
        });

        // Register sanitization callback
        add_filter('redco_optimizer_sanitize_wordpress_core_tweaks_settings', array($this, 'sanitize_settings_for_global_system'), 10, 2);
    }

    /**
     * Get current settings (loads them if not already loaded) - Memory Safe Version
     */
    private function get_current_settings() {
        // Prevent recursion by checking if settings are already being loaded
        if ($this->settings_loaded) {
            return $this->settings;
        }

        // Memory limit check
        $memory_usage = memory_get_usage(true);
        $memory_limit = $this->get_memory_limit_bytes();
        if ($memory_usage > ($memory_limit * 0.8)) {
            error_log("Redco: Memory usage high ({$memory_usage} bytes), returning default settings");
            return $this->get_default_settings();
        }

        // Mark settings as being loaded to prevent recursion
        $this->settings_loaded = true;

        try {
            // Get default settings first to ensure all keys exist
            $default_settings = $this->get_default_settings();

            // Return current settings from database directly to avoid recursion
            $option_name = 'redco_optimizer_wordpress_core_tweaks';
            $saved_settings = get_option($option_name, array());

            // Ensure saved_settings is an array
            if (!is_array($saved_settings)) {
                $saved_settings = array();
            }

            // Merge with defaults to ensure all keys exist, with saved settings taking precedence
            $merged_settings = array_merge($default_settings, $saved_settings);

            // Ensure array values are properly handled
            foreach ($merged_settings as $key => $value) {
                if (in_array($key, array('autosave_post_types', 'exclude_handles', 'query_string_exclude_handles'))) {
                    if (!is_array($value)) {
                        $merged_settings[$key] = $default_settings[$key];
                    }
                }
            }

            // Cache the settings
            $this->settings = $merged_settings;

            return $merged_settings;

        } catch (Exception $e) {
            error_log("Redco: Error loading settings - " . $e->getMessage());
            $this->settings_loaded = false; // Reset flag on error
            return $this->get_default_settings();
        }
    }

    /**
     * Get memory limit in bytes
     */
    private function get_memory_limit_bytes() {
        $memory_limit = ini_get('memory_limit');
        if ($memory_limit == -1) {
            return PHP_INT_MAX;
        }

        $unit = strtolower(substr($memory_limit, -1));
        $value = (int) $memory_limit;

        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Get intelligent recommendations based on current site state
     */
    private function get_intelligent_recommendations() {
        $recommendations = array();

        // Always recommend these safe optimizations
        $safe_optimizations = array(
            'disable_emojis' => true,           // Safe for most sites
            'emoji_remove_feeds' => true,       // Safe for RSS feeds
            'emoji_remove_emails' => true,      // Safe for emails
            'remove_version_numbers' => true,   // Security improvement
            'remove_css_versions' => true,      // Caching improvement
            'remove_js_versions' => true,       // Caching improvement
            'limit_post_revisions' => true,     // Database optimization
            'remove_shortlink' => true          // Clean HTML
        );

        // Conditional recommendations based on site analysis
        $conditional_optimizations = $this->analyze_site_for_recommendations();

        return array_merge($safe_optimizations, $conditional_optimizations);
    }

    /**
     * Analyze site to determine conditional recommendations
     */
    private function analyze_site_for_recommendations() {
        $recommendations = array();

        // Check if XML-RPC is being used
        if (!$this->is_xmlrpc_in_use()) {
            $recommendations['disable_xmlrpc'] = true;
        }

        // Check if pingbacks are beneficial
        if (!$this->are_pingbacks_beneficial()) {
            $recommendations['disable_pingbacks'] = true;
        }

        // Check post revision usage
        if ($this->should_limit_revisions()) {
            $recommendations['limit_post_revisions'] = true;
        }

        return $recommendations;
    }

    /**
     * Check if XML-RPC is actively being used
     */
    private function is_xmlrpc_in_use() {
        // Check for mobile apps or external publishing tools
        $xmlrpc_usage = get_option('redco_xmlrpc_usage_detected', false);

        // Simple heuristic: if no recent XML-RPC requests, likely safe to disable
        return $xmlrpc_usage;
    }

    /**
     * Check if pingbacks are beneficial for this site - Memory Safe Version
     */
    private function are_pingbacks_beneficial() {
        // Memory check before database query
        if (memory_get_usage(true) > ($this->get_memory_limit_bytes() * 0.7)) {
            return false; // Conservative default when memory is high
        }

        try {
            // Check recent pingback activity with limit to prevent memory issues
            global $wpdb;
            $recent_pingbacks = $wpdb->get_var(
                "SELECT COUNT(*) FROM {$wpdb->comments}
                 WHERE comment_type = 'pingback'
                 AND comment_date > DATE_SUB(NOW(), INTERVAL 30 DAY)
                 LIMIT 100"
            );

            return $recent_pingbacks > 0;
        } catch (Exception $e) {
            error_log("Redco: Error checking pingbacks - " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if post revisions should be limited - Memory Safe Version
     */
    private function should_limit_revisions() {
        // Memory check before database query
        if (memory_get_usage(true) > ($this->get_memory_limit_bytes() * 0.7)) {
            return true; // Conservative default when memory is high
        }

        try {
            global $wpdb;

            // Simplified query to prevent memory issues
            $revision_count = $wpdb->get_var(
                "SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'revision'"
            );

            $post_count = $wpdb->get_var(
                "SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'publish' AND post_type IN ('post', 'page')"
            );

            if ($post_count > 0) {
                $avg_revisions = $revision_count / $post_count;
                return $avg_revisions > 10; // If average > 10 revisions per post
            }

            return false;
        } catch (Exception $e) {
            error_log("Redco: Error checking revisions - " . $e->getMessage());
            return true; // Conservative default on error
        }
    }

    /**
     * Get performance impact score for a setting
     */
    private function get_setting_performance_impact($setting_key, $enabled) {
        $impact_scores = array(
            'emoji_remove_frontend' => $enabled ? 15 : -15,
            'remove_wp_version' => $enabled ? 5 : -5,
            'remove_css_versions' => $enabled ? 10 : -10,
            'remove_js_versions' => $enabled ? 10 : -10,
            'limit_revisions' => $enabled ? 8 : -8,
            'disable_xmlrpc' => $enabled ? 3 : -3,
            'disable_pingbacks' => $enabled ? 2 : -2,
            'remove_shortlink' => $enabled ? 1 : -1
        );

        return isset($impact_scores[$setting_key]) ? $impact_scores[$setting_key] : 0;
    }

    /**
     * Validate settings before saving
     */
    private function validate_settings($settings) {
        if (!is_array($settings)) {
            return false;
        }

        // Ensure required keys exist
        $defaults = $this->get_default_settings();
        foreach ($defaults as $key => $default_value) {
            if (!isset($settings[$key])) {
                $settings[$key] = $default_value;
            }
        }

        // Validate specific settings
        if (isset($settings['max_revisions'])) {
            $settings['max_revisions'] = max(1, min(50, intval($settings['max_revisions'])));
        }

        if (isset($settings['autosave_interval'])) {
            $settings['autosave_interval'] = max(30, min(3600, intval($settings['autosave_interval'])));
        }

        return $settings;
    }

    /**
     * Create backup of current settings
     */
    private function create_settings_backup() {
        $current_settings = $this->get_current_settings();
        $backup_data = array(
            'timestamp' => current_time('mysql'),
            'settings' => $current_settings,
            'user_id' => get_current_user_id()
        );

        $backup_option = 'redco_wordpress_core_tweaks_backup_' . date('Y_m_d_H_i_s');
        return update_option($backup_option, $backup_data);
    }

    /**
     * Enhanced cache clearing - Memory Safe Version
     */
    private function clear_optimization_caches() {
        // Memory check before cache operations
        if (memory_get_usage(true) > ($this->get_memory_limit_bytes() * 0.8)) {
            error_log("Redco: Skipping cache clearing due to high memory usage");
            return;
        }

        try {
            // WordPress core cache - minimal memory impact
            if (function_exists('wp_cache_flush')) {
                wp_cache_flush();
            }

            // Object cache - specific group only
            if (function_exists('wp_cache_flush_group')) {
                wp_cache_flush_group('redco_optimizer');
            }

            // Popular caching plugins - with memory monitoring
            $this->clear_popular_plugin_caches();

        } catch (Exception $e) {
            error_log("Redco: Error clearing caches - " . $e->getMessage());
        }
    }

    /**
     * Clear caches from popular caching plugins - Memory Safe Version
     */
    private function clear_popular_plugin_caches() {
        // Check memory before each plugin cache clear
        $memory_threshold = $this->get_memory_limit_bytes() * 0.8;

        try {
            // WP Rocket - check memory first
            if (memory_get_usage(true) < $memory_threshold && function_exists('rocket_clean_domain')) {
                rocket_clean_domain();
            }

            // W3 Total Cache - check memory first
            if (memory_get_usage(true) < $memory_threshold && function_exists('w3tc_flush_all')) {
                w3tc_flush_all();
            }

            // WP Super Cache - check memory first
            if (memory_get_usage(true) < $memory_threshold && function_exists('wp_cache_clear_cache')) {
                wp_cache_clear_cache();
            }

            // LiteSpeed Cache - check memory first
            if (memory_get_usage(true) < $memory_threshold && class_exists('LiteSpeed_Cache_API')) {
                LiteSpeed_Cache_API::purge_all();
            }

        } catch (Exception $e) {
            error_log("Redco: Error clearing plugin caches - " . $e->getMessage());
        }
    }

    /**
     * Generate performance report
     */
    private function generate_performance_report($applied_changes, $total_impact) {
        $report = array(
            'total_impact_score' => $total_impact,
            'impact_level' => $this->get_impact_level($total_impact),
            'optimizations_applied' => count($applied_changes),
            'estimated_benefits' => array()
        );

        if ($total_impact > 0) {
            $report['estimated_benefits'] = array(
                'page_load_improvement' => $this->estimate_page_load_improvement($total_impact),
                'security_enhancement' => $this->estimate_security_improvement($applied_changes),
                'seo_benefits' => $this->estimate_seo_benefits($applied_changes),
                'server_load_reduction' => $this->estimate_server_load_reduction($total_impact)
            );
        }

        return $report;
    }

    /**
     * Get impact level description
     */
    private function get_impact_level($score) {
        if ($score >= 30) return 'High Impact';
        if ($score >= 15) return 'Medium Impact';
        if ($score >= 5) return 'Low Impact';
        return 'Minimal Impact';
    }

    /**
     * Estimate page load improvement
     */
    private function estimate_page_load_improvement($impact_score) {
        $improvement_ms = $impact_score * 10; // Rough estimate: 10ms per impact point
        return array(
            'estimated_ms_saved' => $improvement_ms,
            'percentage_improvement' => min(25, $impact_score * 0.5) // Cap at 25%
        );
    }

    /**
     * Estimate security improvement
     */
    private function estimate_security_improvement($applied_changes) {
        $security_improvements = array();

        if (isset($applied_changes['remove_wp_version'])) {
            $security_improvements[] = 'WordPress version hidden from attackers';
        }

        if (isset($applied_changes['disable_xmlrpc'])) {
            $security_improvements[] = 'XML-RPC attack vector eliminated';
        }

        if (isset($applied_changes['disable_pingbacks'])) {
            $security_improvements[] = 'Pingback spam prevention enabled';
        }

        return $security_improvements;
    }

    /**
     * Estimate SEO benefits
     */
    private function estimate_seo_benefits($applied_changes) {
        $seo_benefits = array();

        if (isset($applied_changes['emoji_remove_frontend'])) {
            $seo_benefits[] = 'Reduced page weight improves Core Web Vitals';
        }

        if (isset($applied_changes['remove_css_versions']) || isset($applied_changes['remove_js_versions'])) {
            $seo_benefits[] = 'Better browser caching improves page speed scores';
        }

        if (isset($applied_changes['remove_shortlink'])) {
            $seo_benefits[] = 'Cleaner HTML structure';
        }

        return $seo_benefits;
    }

    /**
     * Estimate server load reduction
     */
    private function estimate_server_load_reduction($impact_score) {
        return array(
            'cpu_reduction_percentage' => min(15, $impact_score * 0.3),
            'memory_savings_mb' => $impact_score * 0.5,
            'database_query_reduction' => isset($applied_changes['limit_revisions']) ? '10-30%' : '0-5%'
        );
    }

    /**
     * Get success message based on applied changes
     */
    private function get_success_message($changes_count) {
        if ($changes_count === 0) {
            return 'Your site is already optimized with our recommended settings!';
        } elseif ($changes_count === 1) {
            return 'Successfully applied 1 optimization to improve your site performance!';
        } else {
            return sprintf('Successfully applied %d optimizations to boost your site performance!', $changes_count);
        }
    }

    /**
     * Format performance improvement for display
     */
    private function format_performance_improvement($impact_score) {
        if ($impact_score <= 0) {
            return 'No performance improvement expected';
        }

        $improvement_percentage = min(25, $impact_score * 0.5);
        return sprintf('Up to %.1f%% performance improvement expected', $improvement_percentage);
    }

    /**
     * Get next step recommendations
     */
    private function get_next_step_recommendations() {
        return array(
            'Enable Page Caching module for maximum speed boost',
            'Configure Asset Optimization for CSS/JS minification',
            'Set up Lazy Loading for images and videos',
            'Run a performance audit to measure improvements'
        );
    }

    /**
     * Get default settings
     */
    private function get_default_settings() {
        return array(
            'enabled' => true,
            // Emoji settings
            'emoji_remove_frontend' => false,
            'emoji_remove_admin' => false,
            'emoji_remove_feeds' => false,
            'emoji_remove_emails' => false,
            // Version removal settings
            'remove_wp_version' => false,
            'remove_css_versions' => false,
            'remove_js_versions' => false,
            'remove_theme_versions' => false,
            'remove_plugin_versions' => false,
            // Query string settings
            'remove_css_query_strings' => false,
            'remove_js_query_strings' => false,
            'remove_all_query_params' => false,
            // Autosave settings
            'disable_autosave' => false,
            'autosave_interval' => 300,
            'autosave_post_types' => array('post', 'page'),
            // Revision settings
            'limit_revisions' => false,
            'max_revisions' => 5,
            // Security settings
            'disable_xmlrpc' => false,
            'disable_pingbacks' => false,
            'remove_shortlink' => false,
            // Exclusion settings
            'exclude_handles' => array(),
            'query_string_exclude_handles' => array(),
            // Legacy compatibility (for old setting names)
            'disable_emojis' => false,
            'remove_version_numbers' => false
        );
    }
}

/**
 * Helper function to convert human readable memory limit to bytes
 */
if (!function_exists('redco_convert_hr_to_bytes')) {
    function redco_convert_hr_to_bytes($value) {
        if (function_exists('wp_convert_hr_to_bytes')) {
            return wp_convert_hr_to_bytes($value);
        }

        // Fallback for older WordPress versions
        $value = strtolower(trim($value));
        $bytes = (int) $value;

        if (false !== strpos($value, 'g')) {
            $bytes *= 1024 * 1024 * 1024;
        } elseif (false !== strpos($value, 'm')) {
            $bytes *= 1024 * 1024;
        } elseif (false !== strpos($value, 'k')) {
            $bytes *= 1024;
        }

        return $bytes;
    }
}

// Auto-enable the module if not already enabled - Memory Safe Version
add_action('admin_init', function() {
    // Memory check before auto-enable
    if (memory_get_usage(true) > (ini_get('memory_limit') ? redco_convert_hr_to_bytes(ini_get('memory_limit')) * 0.7 : 268435456)) {
        return; // Skip auto-enable if memory usage is high
    }

    static $auto_enable_processed = false;
    if ($auto_enable_processed) {
        return; // Prevent multiple executions
    }
    $auto_enable_processed = true;

    try {
        $options = get_option('redco_optimizer_options', array());
        if (!isset($options['modules_enabled'])) {
            $options['modules_enabled'] = array();
        }
        if (!in_array('wordpress-core-tweaks', $options['modules_enabled'])) {
            $options['modules_enabled'][] = 'wordpress-core-tweaks';
            update_option('redco_optimizer_options', $options);
        }
    } catch (Exception $e) {
        error_log("Redco: Error in auto-enable - " . $e->getMessage());
    }
}, 999); // Lower priority to run after other initialization

// Initialize the module only if enabled and after init hook - Memory Safe Version
function redco_init_wordpress_core_tweaks() {
    // Memory check before initialization
    $memory_usage = memory_get_usage(true);
    $memory_limit = redco_convert_hr_to_bytes(ini_get('memory_limit'));

    if ($memory_limit > 0 && $memory_usage > ($memory_limit * 0.7)) {
        error_log("Redco: Skipping WordPress Core Tweaks initialization due to high memory usage ({$memory_usage} bytes)");
        return;
    }

    static $init_processed = false;
    if ($init_processed) {
        return; // Prevent multiple initializations
    }
    $init_processed = true;

    try {
        if (redco_is_module_enabled('wordpress-core-tweaks')) {
            Redco_WordPress_Core_Tweaks::get_instance();
        }
    } catch (Exception $e) {
        error_log("Redco: Error initializing WordPress Core Tweaks - " . $e->getMessage());
    }
}
add_action('init', 'redco_init_wordpress_core_tweaks', 10);

// Add filter to properly handle textarea fields for WordPress Core Tweaks module
add_filter('redco_optimizer_sanitize_wordpress_core_tweaks_settings', 'redco_sanitize_wordpress_core_tweaks_settings', 10, 2);

function redco_sanitize_wordpress_core_tweaks_settings($sanitized, $raw_settings) {
    // Convert textarea fields to arrays for proper handling
    if (isset($sanitized['exclude_handles']) && is_string($sanitized['exclude_handles'])) {
        $sanitized['exclude_handles'] = !empty($sanitized['exclude_handles'])
            ? array_filter(array_map('trim', explode("\n", $sanitized['exclude_handles'])))
            : array();
    }

    if (isset($sanitized['query_string_exclude_handles']) && is_string($sanitized['query_string_exclude_handles'])) {
        $sanitized['query_string_exclude_handles'] = !empty($sanitized['query_string_exclude_handles'])
            ? array_filter(array_map('trim', explode("\n", $sanitized['query_string_exclude_handles'])))
            : array();
    }

    // Ensure autosave_post_types is always an array
    if (isset($sanitized['autosave_post_types']) && !is_array($sanitized['autosave_post_types'])) {
        $sanitized['autosave_post_types'] = array('post', 'page'); // Default fallback
    }

    return $sanitized;
}
