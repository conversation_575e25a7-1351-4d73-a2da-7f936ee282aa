<?php
/**
 * WordPress Core Tweaks Module for Redco Optimizer
 *
 * Unified module combining emoji removal, version string removal, and autosave optimization.
 * Consolidates: Emoji Stripper, Query String Remover, Asset Version Remover, Autosave Reducer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_WordPress_Core_Tweaks {

    /**
     * Module settings
     */
    private $settings = array();

    /**
     * Constructor
     */
    public function __construct() {
        if (redco_is_module_enabled('wordpress-core-tweaks')) {
            $this->init();
        }
    }

    /**
     * Initialize the module
     */
    private function init() {
        // Load settings
        $this->load_settings();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Load module settings
     */
    private function load_settings() {
        $this->settings = array(
            // Emoji Stripper settings
            'emoji_remove_frontend' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_frontend', true),
            'emoji_remove_admin' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_admin', false),
            'emoji_remove_feeds' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_feeds', true),
            'emoji_remove_emails' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_emails', true),

            // Version Remover settings (combining both query string and asset version removers)
            'remove_css_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_css_versions', true),
            'remove_js_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_js_versions', true),
            'remove_theme_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_theme_versions', true),
            'remove_plugin_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_plugin_versions', true),
            'remove_wp_version' => redco_get_module_option('wordpress-core-tweaks', 'remove_wp_version', true),
            'exclude_handles' => redco_get_module_option('wordpress-core-tweaks', 'exclude_handles', array()),

            // Query String Remover settings
            'remove_css_query_strings' => redco_get_module_option('wordpress-core-tweaks', 'remove_css_query_strings', true),
            'remove_js_query_strings' => redco_get_module_option('wordpress-core-tweaks', 'remove_js_query_strings', true),
            'query_string_exclude_handles' => redco_get_module_option('wordpress-core-tweaks', 'query_string_exclude_handles', array()),
            'remove_all_query_params' => redco_get_module_option('wordpress-core-tweaks', 'remove_all_query_params', false),

            // Autosave Reducer settings
            'autosave_interval' => redco_get_module_option('wordpress-core-tweaks', 'autosave_interval', 300),
            'disable_autosave' => redco_get_module_option('wordpress-core-tweaks', 'disable_autosave', false),
            'autosave_post_types' => redco_get_module_option('wordpress-core-tweaks', 'autosave_post_types', array('post', 'page')),
            'limit_revisions' => redco_get_module_option('wordpress-core-tweaks', 'limit_revisions', true),
            'max_revisions' => redco_get_module_option('wordpress-core-tweaks', 'max_revisions', 5)
        );
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Emoji removal hooks
        $this->init_emoji_hooks();

        // Version removal hooks
        $this->init_version_removal_hooks();

        // Query string removal hooks
        $this->init_query_string_removal_hooks();

        // Autosave optimization hooks
        $this->init_autosave_hooks();

        // AJAX handlers
        add_action('wp_ajax_redco_apply_recommended_tweaks', array($this, 'ajax_apply_recommended_tweaks'));
        add_action('wp_ajax_redco_reset_all_tweaks', array($this, 'ajax_reset_all_tweaks'));
    }

    /**
     * Initialize emoji removal hooks
     */
    private function init_emoji_hooks() {
        // Remove emoji scripts and styles from frontend
        if ($this->settings['emoji_remove_frontend'] && !is_admin()) {
            $this->remove_emoji_hooks();
        }

        // Remove emoji scripts and styles from admin
        if ($this->settings['emoji_remove_admin'] && is_admin()) {
            $this->remove_emoji_hooks();
        }

        // Remove emoji from feeds
        if ($this->settings['emoji_remove_feeds']) {
            remove_filter('the_content_feed', 'wp_staticize_emoji');
            remove_filter('the_excerpt_rss', 'wp_staticize_emoji');
            remove_filter('wp_mail', 'wp_staticize_emoji_for_email');
        }

        // Remove emoji from emails
        if ($this->settings['emoji_remove_emails']) {
            remove_filter('wp_mail', 'wp_staticize_emoji_for_email');
        }
    }

    /**
     * Remove emoji-related hooks
     */
    private function remove_emoji_hooks() {
        // Remove emoji detection script
        remove_action('wp_head', 'print_emoji_detection_script', 7);
        remove_action('admin_print_scripts', 'print_emoji_detection_script');

        // Remove emoji styles
        remove_action('wp_print_styles', 'print_emoji_styles');
        remove_action('admin_print_styles', 'print_emoji_styles');

        // Remove emoji from content
        remove_filter('the_content_feed', 'wp_staticize_emoji');
        remove_filter('the_excerpt_rss', 'wp_staticize_emoji');
        remove_filter('wp_mail', 'wp_staticize_emoji_for_email');

        // Remove emoji DNS prefetch
        add_filter('wp_resource_hints', array($this, 'remove_emoji_dns_prefetch'), 10, 2);

        // Remove TinyMCE emoji plugin
        add_filter('tiny_mce_plugins', array($this, 'remove_tinymce_emoji'));
    }

    /**
     * Remove emoji DNS prefetch
     */
    public function remove_emoji_dns_prefetch($urls, $relation_type) {
        if ('dns-prefetch' === $relation_type) {
            $emoji_svg_url = apply_filters('emoji_svg_url', 'https://s.w.org/images/core/emoji/');
            $urls = array_diff($urls, array($emoji_svg_url));
        }

        return $urls;
    }

    /**
     * Remove TinyMCE emoji plugin
     */
    public function remove_tinymce_emoji($plugins) {
        if (is_array($plugins)) {
            return array_diff($plugins, array('wpemoji'));
        }

        return array();
    }

    /**
     * Initialize version removal hooks
     */
    private function init_version_removal_hooks() {
        // Don't remove versions in admin
        if (is_admin()) {
            return;
        }

        // Add universal null safety filters with highest priority
        add_filter('style_loader_src', array($this, 'ensure_valid_src'), 5, 2);
        add_filter('script_loader_src', array($this, 'ensure_valid_src'), 5, 2);

        // Remove versions from CSS files
        if ($this->settings['remove_css_versions']) {
            add_filter('style_loader_src', array($this, 'remove_asset_version'), 10, 2);
        }

        // Remove versions from JS files
        if ($this->settings['remove_js_versions']) {
            add_filter('script_loader_src', array($this, 'remove_asset_version'), 10, 2);
        }

        // Remove WordPress version from generator tag
        if ($this->settings['remove_wp_version']) {
            remove_action('wp_head', 'wp_generator');
            add_filter('the_generator', '__return_empty_string');
        }
    }

    /**
     * Ensure src is always a valid string to prevent WordPress core ltrim() errors
     */
    public function ensure_valid_src($src, $handle = '') {
        // Convert null or non-string values to empty string
        if (!is_string($src)) {
            return '';
        }

        // Return the valid string as-is
        return $src;
    }

    /**
     * Remove version from asset URLs (combines both query string and asset version removal)
     */
    public function remove_asset_version($src, $handle = '') {
        // Ensure src is not null and is a string - return empty string if invalid
        if (!is_string($src)) {
            return '';
        }

        // Return empty string for empty src to prevent WordPress core issues
        if (empty($src)) {
            return '';
        }

        // Safely handle exclude_handles - convert string to array if needed
        $exclude_handles = $this->settings['exclude_handles'];
        if (is_string($exclude_handles)) {
            $exclude_handles = !empty($exclude_handles) ? explode("\n", $exclude_handles) : array();
        } elseif (!is_array($exclude_handles)) {
            $exclude_handles = array();
        }

        // Skip if handle is excluded
        if (!empty($handle) && in_array($handle, $exclude_handles)) {
            return $src;
        }

        // Skip external files unless specifically allowed
        if (!$this->should_process_url($src)) {
            return $src;
        }

        // Remove all query parameters including version strings
        $parsed_url = parse_url($src);
        if (isset($parsed_url['query'])) {
            $src = str_replace('?' . $parsed_url['query'], '', $src);
        }

        return $src;
    }

    /**
     * Check if URL should be processed for version removal
     */
    private function should_process_url($src) {
        // Ensure src is not null and is a string
        if (!is_string($src) || empty($src)) {
            return false;
        }

        $home_url = home_url();
        $wp_content_url = content_url();
        $wp_includes_url = includes_url();

        // Always process local files
        if (strpos($src, $home_url) === 0) {
            return true;
        }

        // Process WordPress core files
        if (strpos($src, $wp_includes_url) === 0) {
            return true;
        }

        // Process theme files if enabled
        if ($this->settings['remove_theme_versions'] && strpos($src, get_template_directory_uri()) === 0) {
            return true;
        }

        // Process child theme files if enabled
        if ($this->settings['remove_theme_versions'] && get_stylesheet_directory_uri() !== get_template_directory_uri()) {
            if (strpos($src, get_stylesheet_directory_uri()) === 0) {
                return true;
            }
        }

        // Process plugin files if enabled
        if ($this->settings['remove_plugin_versions'] && strpos($src, plugins_url()) === 0) {
            return true;
        }

        return false;
    }

    /**
     * Initialize query string removal hooks
     */
    private function init_query_string_removal_hooks() {
        // Don't remove query strings in admin
        if (is_admin()) {
            return;
        }

        // Add universal null safety filters with highest priority (if not already added)
        if (!has_filter('style_loader_src', array($this, 'ensure_valid_src'))) {
            add_filter('style_loader_src', array($this, 'ensure_valid_src'), 5, 2);
        }
        if (!has_filter('script_loader_src', array($this, 'ensure_valid_src'))) {
            add_filter('script_loader_src', array($this, 'ensure_valid_src'), 5, 2);
        }

        // Remove query strings from CSS files
        if ($this->settings['remove_css_query_strings']) {
            add_filter('style_loader_src', array($this, 'remove_query_strings'), 15, 2);
        }

        // Remove query strings from JS files
        if ($this->settings['remove_js_query_strings']) {
            add_filter('script_loader_src', array($this, 'remove_query_strings'), 15, 2);
        }
    }

    /**
     * Remove query strings from asset URLs (more aggressive than version removal)
     */
    public function remove_query_strings($src, $handle = '') {
        // Ensure src is not null and is a string - return empty string if invalid
        if (!is_string($src)) {
            return '';
        }

        // Return empty string for empty src to prevent WordPress core issues
        if (empty($src)) {
            return '';
        }

        // Safely handle query_string_exclude_handles - convert string to array if needed
        $query_exclude_handles = $this->settings['query_string_exclude_handles'];
        if (is_string($query_exclude_handles)) {
            $query_exclude_handles = !empty($query_exclude_handles) ? explode("\n", $query_exclude_handles) : array();
        } elseif (!is_array($query_exclude_handles)) {
            $query_exclude_handles = array();
        }

        // Skip if handle is excluded
        if (!empty($handle) && in_array($handle, $query_exclude_handles)) {
            return $src;
        }

        // Skip external files unless specifically allowed
        if (!$this->should_process_query_string_url($src)) {
            return $src;
        }

        // Remove all query parameters if aggressive mode is enabled
        if ($this->settings['remove_all_query_params']) {
            $parsed_url = parse_url($src);
            if (isset($parsed_url['query'])) {
                $src = str_replace('?' . $parsed_url['query'], '', $src);
            }
        } else {
            // Remove only common query parameters (more conservative)
            $src = preg_replace('/\?.*$/', '', $src);
        }

        return $src;
    }

    /**
     * Check if URL should be processed for query string removal
     */
    private function should_process_query_string_url($src) {
        // Ensure src is not null before using strpos
        $src = (string) $src;
        if (empty($src)) {
            return false;
        }

        $home_url = home_url();
        $wp_content_url = content_url();
        $wp_includes_url = includes_url();

        // Always process local files
        if (strpos($src, $home_url) === 0) {
            return true;
        }

        // Process WordPress core files
        if (strpos($src, $wp_includes_url) === 0) {
            return true;
        }

        // Process content files (themes, plugins, uploads)
        if (strpos($src, $wp_content_url) === 0) {
            return true;
        }

        return false;
    }

    /**
     * Initialize autosave optimization hooks
     */
    private function init_autosave_hooks() {
        // Only apply in admin area
        if (!is_admin()) {
            return;
        }

        // Disable autosave completely if setting is enabled
        if ($this->settings['disable_autosave']) {
            add_action('wp_print_scripts', array($this, 'disable_autosave_script'));
        } else {
            // Modify autosave interval
            add_action('wp_print_scripts', array($this, 'modify_autosave_interval'));
        }

        // Limit post revisions if enabled
        if ($this->settings['limit_revisions']) {
            add_filter('wp_revisions_to_keep', array($this, 'limit_post_revisions'), 10, 2);
        }
    }

    /**
     * Disable autosave script completely
     */
    public function disable_autosave_script() {
        global $pagenow;

        // Only on post edit pages
        if (!in_array($pagenow, array('post.php', 'post-new.php'))) {
            return;
        }

        // Check if current post type is in allowed list
        if (!$this->is_allowed_post_type()) {
            return;
        }

        wp_deregister_script('autosave');
    }

    /**
     * Modify autosave interval
     */
    public function modify_autosave_interval() {
        global $pagenow;

        // Only on post edit pages
        if (!in_array($pagenow, array('post.php', 'post-new.php'))) {
            return;
        }

        // Check if current post type is in allowed list
        if (!$this->is_allowed_post_type()) {
            return;
        }

        $interval = $this->settings['autosave_interval'];

        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Override autosave interval
            if (typeof wp !== 'undefined' && wp.autosave) {
                wp.autosave.server.tempBlockSave = function() {
                    return false;
                };

                // Set new interval
                if (wp.autosave.server.triggerSave) {
                    clearInterval(wp.autosave.server.triggerSave);
                    wp.autosave.server.triggerSave = setInterval(function() {
                        wp.autosave.server.triggerSave();
                    }, <?php echo intval($interval * 1000); ?>);
                }
            }

            // Also modify heartbeat settings for autosave
            if (typeof wp !== 'undefined' && wp.heartbeat) {
                wp.heartbeat.interval(<?php echo intval($interval); ?>);
            }
        });
        </script>
        <?php
    }

    /**
     * Check if current post type is allowed for autosave modifications
     */
    private function is_allowed_post_type() {
        global $post, $pagenow;

        $post_type = '';

        if ($pagenow === 'post-new.php' && isset($_GET['post_type'])) {
            $post_type = sanitize_text_field($_GET['post_type']);
        } elseif ($pagenow === 'post.php' && isset($post) && $post->post_type) {
            $post_type = $post->post_type;
        } elseif (isset($_GET['post']) && is_numeric($_GET['post'])) {
            $post_obj = get_post(intval($_GET['post']));
            if ($post_obj) {
                $post_type = $post_obj->post_type;
            }
        }

        // Safely handle autosave_post_types - ensure it's an array
        $autosave_post_types = $this->settings['autosave_post_types'];
        if (!is_array($autosave_post_types)) {
            $autosave_post_types = array('post', 'page'); // Default fallback
        }

        return in_array($post_type, $autosave_post_types);
    }

    /**
     * Limit post revisions
     */
    public function limit_post_revisions($num, $post) {
        // Safely handle autosave_post_types - ensure it's an array
        $autosave_post_types = $this->settings['autosave_post_types'];
        if (!is_array($autosave_post_types)) {
            $autosave_post_types = array('post', 'page'); // Default fallback
        }

        // Check if post type is in allowed list
        if (!in_array($post->post_type, $autosave_post_types)) {
            return $num;
        }

        return $this->settings['max_revisions'];
    }

    /**
     * Get comprehensive statistics for all features
     */
    public function get_stats() {
        $wp_default_autosave = 60; // WordPress default autosave interval
        $current_interval = $this->settings['disable_autosave'] ? 0 : $this->settings['autosave_interval'];

        // Safely handle exclude_handles - convert string to array if needed
        $exclude_handles = $this->settings['exclude_handles'];
        if (is_string($exclude_handles)) {
            $exclude_handles = !empty($exclude_handles) ? explode("\n", $exclude_handles) : array();
        } elseif (!is_array($exclude_handles)) {
            $exclude_handles = array();
        }

        // Safely handle query_string_exclude_handles - convert string to array if needed
        $query_exclude_handles = $this->settings['query_string_exclude_handles'];
        if (is_string($query_exclude_handles)) {
            $query_exclude_handles = !empty($query_exclude_handles) ? explode("\n", $query_exclude_handles) : array();
        } elseif (!is_array($query_exclude_handles)) {
            $query_exclude_handles = array();
        }

        $stats = array(
            // Emoji removal stats
            'emoji_frontend_removed' => $this->settings['emoji_remove_frontend'],
            'emoji_admin_removed' => $this->settings['emoji_remove_admin'],
            'emoji_feeds_removed' => $this->settings['emoji_remove_feeds'],
            'emoji_emails_removed' => $this->settings['emoji_remove_emails'],
            'emoji_estimated_savings' => '15-20 KB per page',

            // Version removal stats
            'css_versions_removed' => $this->settings['remove_css_versions'],
            'js_versions_removed' => $this->settings['remove_js_versions'],
            'theme_versions_removed' => $this->settings['remove_theme_versions'],
            'plugin_versions_removed' => $this->settings['remove_plugin_versions'],
            'wp_version_removed' => $this->settings['remove_wp_version'],
            'excluded_handles' => count($exclude_handles),

            // Query string removal stats
            'css_query_strings_removed' => $this->settings['remove_css_query_strings'],
            'js_query_strings_removed' => $this->settings['remove_js_query_strings'],
            'aggressive_query_removal' => $this->settings['remove_all_query_params'],
            'query_excluded_handles' => count($query_exclude_handles),

            // Autosave optimization stats
            'autosave_disabled' => $this->settings['disable_autosave'],
            'current_autosave_interval' => $current_interval,
            'wp_default_autosave_interval' => $wp_default_autosave,
            'autosave_reduction_percentage' => $this->settings['disable_autosave'] ? 100 :
                round((($current_interval - $wp_default_autosave) / $wp_default_autosave) * 100, 1),
            'affected_post_types' => $this->settings['autosave_post_types'],
            'revisions_limited' => $this->settings['limit_revisions'],
            'max_revisions' => $this->settings['max_revisions']
        );

        return $stats;
    }

    /**
     * AJAX handler for applying recommended tweaks
     */
    public function ajax_apply_recommended_tweaks() {
        // Security check
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce') || !current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Security check failed'));
            return;
        }

        $recommended_settings = isset($_POST['settings']) ? $_POST['settings'] : array();

        // Map JavaScript setting names to actual setting names
        $setting_map = array(
            'disable_emojis' => 'disable_emojis',
            'remove_version_numbers' => 'remove_wp_version',
            'disable_xmlrpc' => 'disable_xmlrpc',
            'limit_post_revisions' => 'limit_revisions',
            'disable_pingbacks' => 'disable_pingbacks',
            'remove_shortlink' => 'remove_shortlink'
        );

        // Get current settings
        $current_settings = $this->get_settings();

        // Apply recommended settings
        foreach ($recommended_settings as $js_key => $value) {
            if (isset($setting_map[$js_key])) {
                $setting_key = $setting_map[$js_key];
                $current_settings[$setting_key] = (bool) $value;
            }
        }

        // Save settings
        $option_name = 'redco_optimizer_wordpress_core_tweaks';
        update_option($option_name, $current_settings);

        // Clear any relevant caches
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        wp_send_json_success(array(
            'message' => 'Recommended tweaks applied successfully!',
            'applied_settings' => $recommended_settings
        ));
    }

    /**
     * AJAX handler for resetting all tweaks
     */
    public function ajax_reset_all_tweaks() {
        // Security check
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce') || !current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Security check failed'));
            return;
        }

        // Reset to default settings
        $default_settings = $this->get_default_settings();
        $option_name = 'redco_optimizer_wordpress_core_tweaks';
        update_option($option_name, $default_settings);

        // Clear any relevant caches
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        wp_send_json_success(array(
            'message' => 'All tweaks have been reset to default settings!',
            'reset_settings' => $default_settings
        ));
    }

    /**
     * Get default settings
     */
    private function get_default_settings() {
        return array(
            'enabled' => true,
            'disable_emojis' => false,
            'remove_wp_version' => false,
            'remove_css_versions' => false,
            'remove_js_versions' => false,
            'remove_theme_versions' => false,
            'remove_plugin_versions' => false,
            'remove_css_query_strings' => false,
            'remove_js_query_strings' => false,
            'remove_all_query_params' => false,
            'disable_autosave' => false,
            'autosave_interval' => 60,
            'limit_revisions' => false,
            'max_revisions' => 5,
            'autosave_post_types' => array('post', 'page'),
            'disable_xmlrpc' => false,
            'disable_pingbacks' => false,
            'remove_shortlink' => false,
            'exclude_handles' => array(),
            'query_string_exclude_handles' => array()
        );
    }
}

// Auto-enable the module if not already enabled
add_action('admin_init', function() {
    $options = get_option('redco_optimizer_options', array());
    if (!isset($options['modules_enabled'])) {
        $options['modules_enabled'] = array();
    }
    if (!in_array('wordpress-core-tweaks', $options['modules_enabled'])) {
        $options['modules_enabled'][] = 'wordpress-core-tweaks';
        update_option('redco_optimizer_options', $options);
    }
});

// Initialize the module only if enabled and after init hook
function redco_init_wordpress_core_tweaks() {
    if (redco_is_module_enabled('wordpress-core-tweaks')) {
        new Redco_WordPress_Core_Tweaks();
    }
}
add_action('init', 'redco_init_wordpress_core_tweaks', 10);

// Add filter to properly handle textarea fields for WordPress Core Tweaks module
add_filter('redco_optimizer_sanitize_wordpress_core_tweaks_settings', 'redco_sanitize_wordpress_core_tweaks_settings', 10, 2);

function redco_sanitize_wordpress_core_tweaks_settings($sanitized, $raw_settings) {
    // Convert textarea fields to arrays for proper handling
    if (isset($sanitized['exclude_handles']) && is_string($sanitized['exclude_handles'])) {
        $sanitized['exclude_handles'] = !empty($sanitized['exclude_handles'])
            ? array_filter(array_map('trim', explode("\n", $sanitized['exclude_handles'])))
            : array();
    }

    if (isset($sanitized['query_string_exclude_handles']) && is_string($sanitized['query_string_exclude_handles'])) {
        $sanitized['query_string_exclude_handles'] = !empty($sanitized['query_string_exclude_handles'])
            ? array_filter(array_map('trim', explode("\n", $sanitized['query_string_exclude_handles'])))
            : array();
    }

    // Ensure autosave_post_types is always an array
    if (isset($sanitized['autosave_post_types']) && !is_array($sanitized['autosave_post_types'])) {
        $sanitized['autosave_post_types'] = array('post', 'page'); // Default fallback
    }

    return $sanitized;
}
