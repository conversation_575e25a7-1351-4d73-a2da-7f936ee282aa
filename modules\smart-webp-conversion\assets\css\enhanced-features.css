/**
 * Enhanced WebP Module Features CSS
 * Modern styling for dynamic button states, recent conversions, and UI improvements
 */

/* Auto-refresh indicator styles */
.dashicons.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Dynamic Button States */
#conversion-button-container {
    position: relative;
}

#bulk-convert-images {
    /* transition removed */
    position: relative;
    overflow: hidden;
}

#bulk-convert-images:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

#bulk-convert-images.button-primary {
    background: #4CAF50;
    border-color: #4CAF50;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
}

#bulk-convert-images.button-primary:hover:not(:disabled) {
    background: #45a049;
    border-color: #45a049;
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
    transform: translateY(-1px);
}



#convert-button-spinner {
    display: inline-block;
    margin-left: 8px;
}

#conversion-status-message {
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
}

#conversion-status-message .dashicons {
    vertical-align: middle;
    margin-top: -2px;
}

/* Recent Conversions */
.sidebar-section-header {
    position: relative;
}

.section-actions {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

#conversions-sort {
    border-radius: 4px;
    border: 1px solid #ddd;
    background: white;
}

/* CLEAN OVERHAUL: Recent Conversions List */
#recent-conversions-list {
    max-height: 400px;
    overflow-y: auto;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

/* CLEAN OVERHAUL: Individual Conversion Items */
.recent-conversion-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
}

.recent-conversion-item:last-child {
    border-bottom: none;
}

.recent-conversion-item:hover {
    background-color: #f8f9fa;
}

/* CLEAN OVERHAUL: Conversion Content */
.conversion-main {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.conversion-title {
    font-weight: 600;
    font-size: 13px;
    color: #1d2327;
    line-height: 1.3;
    margin: 0;
}

.conversion-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 11px;
    color: #646970;
    flex-wrap: wrap;
}

.conversion-meta .original-size {
    color: #d63638;
    font-weight: 500;
}

.conversion-meta .arrow {
    color: #8c8f94;
    font-weight: bold;
}

.conversion-meta .webp-size {
    color: #00a32a;
    font-weight: 500;
}

.conversion-meta .savings {
    color: #4CAF50;
    font-weight: 600;
    background: #f0f8f0;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
}

.conversion-meta .date {
    color: #8c8f94;
    margin-left: auto;
    font-size: 10px;
}

.stat-row.savings .stat-value {
    color: #4CAF50;
}

.stat-row.high-savings .stat-value {
    color: #2E7D32;
    font-weight: 700;
}

.stat-row.medium-savings .stat-value {
    color: #4CAF50;
}

.stat-row.low-savings .stat-value {
    color: #FF9800;
}

/* Loading and Empty States */
#conversions-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px;
    color: #666;
    font-size: 14px;
}

#conversions-loading .spinner {
    margin-right: 8px;
}

#conversions-empty {
    text-align: center;
    padding: 30px 20px;
    color: #999;
}

#conversions-empty .dashicons {
    display: block;
    margin: 0 auto 12px;
    opacity: 0.5;
}

#conversions-empty p {
    margin: 0 0 8px 0;
    font-size: 14px;
}

/* Load More Button */
#conversions-load-more {
    padding: 10px;
    text-align: center;
}

#load-more-conversions {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #666;
    transition: all 0.2s ease;
}

#load-more-conversions:hover {
    background: #e9ecef;
    border-color: #dee2e6;
    color: #495057;
}

#load-more-conversions .dashicons {
    margin-right: 4px;
}

/* Pagination Info */
#conversions-pagination-info {
    text-align: center;
    color: #999;
    font-size: 10px; /* Smaller font for compact display */
    padding: 6px; /* Reduced padding for compact display */
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    line-height: 1.2; /* Tighter line height */
}

/* Responsive Design */
@media (max-width: 768px) {
    .conversion-header {
        flex-wrap: wrap;
        gap: 8px;
    }

    /* REMOVED: .conversion-actions responsive styles (no longer needed) */

    .conversion-stats {
        grid-template-columns: 1fr;
        gap: 2px;
    }

    .section-actions {
        position: static;
        transform: none;
        margin-top: 8px;
    }

    .sidebar-section-header {
        flex-direction: column;
        align-items: flex-start;
    }
}

/* Animation for new items */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        /* transform removed */
    }
    to {
        opacity: 1;
        /* transform removed */
    }
}

.conversion-item.new-item {
    animation: slideInFromTop 0.3s ease-out;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .conversion-item {
        border: 1px solid #666;
    }

    .conversion-item:hover {
        border-color: #000;
    }

    .stat-row.savings .stat-value {
        color: #000;
        font-weight: 700;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    #bulk-convert-images,
    .conversion-item,
    #load-more-conversions {
        transition: none;
    }

    #bulk-convert-images.button-primary:hover:not(:disabled) {
        /* transform removed */
    }

    .conversion-item.new-item {
        animation: none;
    }
}

/* Focus styles for accessibility */
.conversion-item:focus-within {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
}

/* REMOVED: .view-details:focus styles (no longer needed) */

#conversions-sort:focus {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
}

/* ===== COMPACT MODAL IMPROVEMENTS ===== */

/* Compact Modal Layout */
.compact-modal {
    max-width: 600px;
    max-height: 85vh;
}

.compact-modal .redco-modal-body {
    padding: 15px 20px;
}

.compact-modal .redco-modal-header {
    padding: 15px 20px 0;
}

.compact-modal .redco-modal-footer {
    padding: 0 20px 15px;
}

/* Left-aligned Progress Stats - Override existing center alignment */
.compact-modal .progress-stats.left-aligned {
    text-align: left !important;
    font-size: 13px;
    color: #6c757d;
    margin-bottom: 15px;
    font-weight: 500;
    width: auto !important;
    display: inline-block;
}

/* Additional override for any progress stats in compact modal */
.compact-modal .progress-stats {
    text-align: left !important;
}

/* Compact Progress Container */
.compact-modal .progress-container {
    margin-bottom: 15px;
}

.compact-modal .progress-bar {
    margin-bottom: 8px;
}

/* Simple, Small Stats Cards */
.conversion-stats.single-row {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    padding: 0;
    background: none;
    border-radius: 0;
}

.conversion-stats.single-row .stat-card {
    flex: 1;
    text-align: center;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px 6px;
    min-width: 0;
}

.conversion-stats.single-row .stat-label {
    display: block;
    font-size: 10px;
    color: #6c757d;
    margin-bottom: 3px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    font-weight: 600;
    line-height: 1;
}

.conversion-stats.single-row .stat-value {
    display: block;
    font-size: 13px;
    font-weight: 700;
    color: #495057;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1;
}

/* Compact Current Operation */
.compact-modal .current-operation {
    margin-bottom: 15px;
    padding: 10px 12px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
}

.compact-modal .current-file {
    font-size: 13px;
    color: #856404;
    font-weight: 500;
    line-height: 1.3;
}

/* ===== COLLAPSIBLE LOG SECTION ===== */

.conversion-log-section {
    margin-top: 15px;
}

.log-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.log-toggle:hover {
    background: #e9ecef;
    border-color: #dee2e6;
}

.log-toggle .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    color: #6c757d;
    transition: transform 0.2s ease;
}

.log-toggle-text {
    font-weight: 600;
    font-size: 12px;
    color: #495057;
    flex: 1;
}

.log-count {
    font-size: 10px;
    color: #6c757d;
    font-weight: 500;
}

/* Collapsible Log Content */
.conversion-log {
    border: 1px solid #e9ecef;
    border-top: none;
    border-radius: 0 0 4px 4px;
    background: #fafafa;
    transition: all 0.3s ease;
    overflow: hidden;
}

.conversion-log.collapsed {
    max-height: 0;
    border-bottom: none;
    opacity: 0;
}

.conversion-log.expanded {
    max-height: 200px;
    opacity: 1;
}

.conversion-log .log-entries {
    padding: 6px;
    max-height: 200px;
    overflow-y: auto;
    background: white;
}

/* Enhanced Log Entries */
.log-entry {
    padding: 4px 6px;
    border-bottom: 1px solid #f1f3f4;
    font-size: 11px;
    display: flex;
    align-items: flex-start;
    gap: 6px;
    line-height: 1.3;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-entry .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
    margin-top: 1px;
    flex-shrink: 0;
}

.log-entry.log-success {
    color: #28a745;
}

.log-entry.log-success .dashicons {
    color: #28a745;
}

.log-entry.log-error {
    color: #dc3545;
}

.log-entry.log-error .dashicons {
    color: #dc3545;
}

.log-entry.log-complete {
    color: #4CAF50;
    font-weight: 600;
    background: #f0f8f0;
    border-radius: 3px;
    margin: 2px 0;
}

.log-entry.log-complete .dashicons {
    color: #4CAF50;
}

.log-entry.log-debug {
    color: #6c757d;
    background: #f8f9fa;
    font-family: monospace;
    font-size: 10px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .compact-modal {
        max-width: 95%;
        margin: 10px;
        max-height: 90vh;
    }

    .conversion-stats.single-row {
        flex-direction: column;
        gap: 6px;
    }

    .conversion-stats.single-row .stat-card {
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: left;
        padding: 6px 8px;
    }

    .conversion-stats.single-row .stat-label {
        margin-bottom: 0;
    }
}

/* ===== TOTAL STATS STYLING ===== */

/* Smaller font sizes for all stat values */
.stats-grid .stat-value {
    font-size: 14px !important; /* Smaller font size for stat values */
    font-weight: 600;
    line-height: 1.2;
}

/* Specific styling for different stat types */
.stat-item.stat-total-files .stat-value {
    color: #1d2327;
}

.stat-item.stat-converted-files .stat-value {
    color: #2196F3;
}

.stat-item.stat-total-savings .stat-value {
    color: #4CAF50;
    font-weight: 600;
}

.stat-item.stat-avg-savings .stat-value {
    color: #4CAF50;
    font-weight: 700;
}

/* ===== TOAST NOTIFICATIONS ===== */

/* Ensure toast notifications are properly positioned above everything */
.redco-toast-container {
    z-index: 999999 !important;
}

/* Additional styling for WebP module specific toasts */
.redco-toast.webp-module {
    border-left: 4px solid #4CAF50;
}

.redco-toast.webp-module .redco-toast-icon {
    color: #4CAF50;
}

/* ===== MODAL STYLES ===== */

/* Modal overlay */
.redco-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999999;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
    opacity: 0;
    transition: opacity 0.3s ease;
    cursor: pointer;
}

/* Show modal with proper centering */
.redco-modal.show {
    display: flex !important;
    opacity: 1;
}

/* Modal content container */
.redco-modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 90vw;
    max-height: 90vh;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease;
    cursor: default;
}

/* Modal content animation when shown */
.redco-modal.show .redco-modal-content {
    transform: scale(1);
}

/* Modal header */
.redco-modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
}

.redco-modal-header h2,
.redco-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1d2327;
}

/* Modal close button */
.redco-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    line-height: 1;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.redco-modal-close:hover {
    background: #e9ecef;
    color: #333;
}

/* Modal body */
.redco-modal-body {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
}

/* Modal footer */
.redco-modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* WebP Progress Modal */
#webp-progress-modal {
    z-index: 999999;
}

#webp-progress-modal .redco-modal-content {
    max-width: 600px;
}

/* WebP Test Modal specific sizing */
.webp-test-modal {
    max-width: 500px;
    min-width: 400px;
}

/* Ensure test results modal is properly sized */
#test-results-modal .redco-modal-content {
    max-width: 600px;
}

/* Responsive modal */
@media (max-width: 768px) {
    .redco-modal {
        padding: 10px;
        align-items: flex-start;
        padding-top: 20px;
    }

    .redco-modal-content {
        max-width: 100%;
        max-height: calc(100vh - 40px);
        width: calc(100% - 20px);
    }

    .redco-modal-header,
    .redco-modal-body,
    .redco-modal-footer {
        padding: 16px;
    }

    .webp-test-modal {
        min-width: auto;
        width: 100%;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .redco-modal {
        padding: 5px;
        padding-top: 10px;
    }

    .redco-modal-content {
        max-height: calc(100vh - 20px);
        width: calc(100% - 10px);
    }

    .redco-modal-header,
    .redco-modal-body,
    .redco-modal-footer {
        padding: 12px;
    }

    .redco-modal-header h2,
    .redco-modal-header h3 {
        font-size: 16px;
    }
}

/* ===== STATS REFRESH VISUAL FEEDBACK ===== */

/* Loading state for stats during refresh */
.stat-value.updating,
.header-metric-value.updating {
    opacity: 0.6;
    position: relative;
    color: #666;
}

.stat-value.updating::after,
.header-metric-value.updating::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    margin: -6px 0 0 -6px;
    border: 2px solid #ddd;
    border-top-color: #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Updated state animation */
.stat-value.updated,
.header-metric-value.updated {
    background-color: #e8f5e8;
    color: #2e7d32;
    padding: 2px 6px;
    border-radius: 3px;
    transition: all 0.3s ease;
}

/* Refresh button loading state */
#refresh-webp-stats:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

#refresh-webp-stats:disabled::before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border: 2px solid #ddd;
    border-top-color: #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Spin animation for loading indicators */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ============================================================================
   RESTORATION SYSTEM STYLES
   ============================================================================ */

/* Restoration stats styling */
.restoration-stats {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 15px;
}

.restoration-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    border-bottom: 1px solid #e0e0e0;
}

.restoration-stat-item:last-child {
    border-bottom: none;
}

.restoration-stat-item .stat-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.restoration-stat-item .stat-value {
    font-size: 13px;
    font-weight: 600;
    color: #333;
}

/* Restoration button styling */
#restore-all-images {
    border-color: #d63638 !important;
    color: #d63638 !important;
    background: #fff;
    transition: all 0.2s ease;
}

#restore-all-images:hover {
    background: #d63638 !important;
    color: #fff !important;
    border-color: #d63638 !important;
}

#restore-all-images:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Spinning animation for restoration button */
#restore-all-images .dashicons.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Restoration warning styling */
.restoration-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 8px;
    font-size: 12px;
    margin-top: 10px;
}

.restoration-warning .dashicons {
    color: #856404;
    margin-right: 5px;
}

/* Restoration status message */
#restoration-status-message {
    margin-bottom: 10px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #d63638;
    font-size: 12px;
    color: #666;
}

/* Media Library restoration controls */
.redco-restore-single {
    color: #d63638 !important;
    text-decoration: none;
}

.redco-restore-single:hover {
    color: #a02622 !important;
}

/* Edit media page restoration button */
.misc-pub-webp-restore {
    border-top: 1px solid #ddd;
    padding-top: 10px;
    margin-top: 10px;
}

.misc-pub-webp-restore label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
}

.misc-pub-webp-restore .button {
    margin-top: 5px;
}

/* Deactivation notice styling */
#redco-webp-deactivation-notice {
    border-left-color: #d63638;
}

#redco-webp-deactivation-notice h3 {
    margin-top: 0;
    color: #d63638;
}

#redco-webp-deactivation-notice .button-link-delete {
    color: #d63638;
    text-decoration: none;
}

#redco-webp-deactivation-notice .button-link-delete:hover {
    color: #a02622;
}
