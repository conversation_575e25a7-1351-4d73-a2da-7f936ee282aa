<?php
/**
 * Emergency Memory Handler for WordPress Core Tweaks
 * 
 * This file provides emergency functionality when the main class cannot be loaded
 * due to memory constraints. It operates completely independently.
 * 
 * @package Redco_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Emergency memory circuit breaker
 * Prevents any WordPress Core Tweaks functionality if memory is critically high
 */
function redco_emergency_memory_check() {
    $memory_usage = memory_get_usage(true);
    $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
    
    // If memory limit is 0 (unlimited), allow normal operation
    if ($memory_limit <= 0) {
        return false;
    }
    
    // Emergency threshold: 75% of memory limit (384MB of 512MB)
    $emergency_threshold = $memory_limit * 0.75;
    
    return ($memory_usage >= $emergency_threshold);
}

/**
 * Check if we should use emergency mode
 */
function redco_should_use_emergency_mode() {
    static $emergency_mode = null;
    
    if ($emergency_mode === null) {
        $emergency_mode = redco_emergency_memory_check();
        
        if ($emergency_mode) {
            // Log emergency mode activation
            error_log("Redco Emergency: Memory usage critical (" . memory_get_usage(true) . " bytes), activating emergency mode");
        }
    }
    
    return $emergency_mode;
}

/**
 * Emergency AJAX handler for Apply Recommended
 * This function operates with absolute minimal memory footprint
 */
function redco_emergency_apply_recommended() {
    // Immediate memory check
    if (redco_emergency_memory_check()) {
        header('Content-Type: application/json');
        echo json_encode(array(
            'success' => false,
            'data' => array(
                'message' => 'Server memory critically low. Please contact administrator.',
                'code' => 'MEMORY_CRITICAL'
            )
        ));
        exit;
    }

    // Security check
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
        header('Content-Type: application/json');
        echo json_encode(array(
            'success' => false,
            'data' => array(
                'message' => 'Security check failed.',
                'code' => 'SECURITY_FAILED'
            )
        ));
        exit;
    }

    if (!current_user_can('manage_options')) {
        header('Content-Type: application/json');
        echo json_encode(array(
            'success' => false,
            'data' => array(
                'message' => 'Insufficient permissions.',
                'code' => 'PERMISSION_DENIED'
            )
        ));
        exit;
    }

    // Emergency mode: Apply only the most critical WordPress optimizations
    $emergency_settings = array(
        'emoji_remove_frontend' => true,
        'remove_wp_version' => true,
        'disable_xmlrpc' => true,
        'limit_revisions' => true,
        'max_revisions' => 3  // Lower than normal to save memory
    );

    // Get current settings (minimal operation)
    $current = get_option('redco_optimizer_wordpress_core_tweaks', array());
    if (!is_array($current)) {
        $current = array();
    }

    // Count changes
    $changes = 0;
    foreach ($emergency_settings as $key => $value) {
        if (!isset($current[$key]) || $current[$key] !== $value) {
            $changes++;
        }
    }

    // Apply settings
    $updated = array_merge($current, $emergency_settings);
    $saved = update_option('redco_optimizer_wordpress_core_tweaks', $updated);

    // Send minimal response
    header('Content-Type: application/json');
    if ($saved || $changes === 0) {
        echo json_encode(array(
            'success' => true,
            'data' => array(
                'message' => $changes > 0 
                    ? "Applied {$changes} critical WordPress optimizations (Emergency Mode)" 
                    : 'Critical WordPress settings already optimized!',
                'total_optimizations' => $changes,
                'emergency_mode' => true,
                'code' => 'SUCCESS'
            )
        ));
    } else {
        echo json_encode(array(
            'success' => false,
            'data' => array(
                'message' => 'Failed to save settings.',
                'code' => 'SAVE_FAILED'
            )
        ));
    }
    
    exit;
}

/**
 * Register emergency AJAX handler
 * This runs very early and bypasses all class loading
 */
add_action('wp_ajax_redco_apply_recommended_tweaks', 'redco_emergency_apply_recommended', 1);

/**
 * Emergency settings page content
 * Provides minimal interface when full class cannot be loaded
 */
function redco_emergency_settings_content() {
    ?>
    <div class="redco-emergency-mode">
        <div class="notice notice-warning">
            <p><strong>Emergency Mode Active:</strong> Server memory is critically low. Only essential WordPress Core optimizations are available.</p>
        </div>
        
        <div class="redco-emergency-settings">
            <h3>Critical WordPress Core Optimizations</h3>
            <p>Click the button below to apply essential WordPress optimizations that improve security and performance:</p>
            
            <ul class="redco-emergency-list">
                <li>✓ Remove emoji scripts (reduces frontend load)</li>
                <li>✓ Hide WordPress version (security improvement)</li>
                <li>✓ Disable XML-RPC (security improvement)</li>
                <li>✓ Limit post revisions to 3 (database optimization)</li>
            </ul>
            
            <p>
                <button type="button" id="apply-recommended-tweaks" class="button button-primary">
                    <span class="dashicons dashicons-performance"></span>
                    Apply Critical Optimizations
                </button>
            </p>
            
            <p class="description">
                <strong>Note:</strong> Full WordPress Core Tweaks functionality is temporarily unavailable due to memory constraints. 
                Contact your hosting provider to increase PHP memory limit for access to all features.
            </p>
        </div>
    </div>
    
    <style>
    .redco-emergency-mode {
        max-width: 800px;
        margin: 20px 0;
    }
    .redco-emergency-settings {
        background: #fff;
        border: 1px solid #ccd0d4;
        padding: 20px;
        border-radius: 4px;
    }
    .redco-emergency-list {
        background: #f9f9f9;
        padding: 15px 20px;
        border-left: 4px solid #00a32a;
        margin: 15px 0;
    }
    .redco-emergency-list li {
        margin: 8px 0;
        color: #00a32a;
        font-weight: 500;
    }
    </style>
    <?php
}
