<?php
/**
 * Test script to verify the Optimization Opportunities fix
 * 
 * This script tests:
 * 1. AJAX parameter compatibility
 * 2. Module enable/disable persistence
 * 3. Opportunities list refresh functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('This script must be run within WordPress context');
}

class Redco_Optimization_Opportunities_Test {
    
    /**
     * Run all tests
     */
    public static function run_tests() {
        echo "<h2>Redco Optimizer - Optimization Opportunities Fix Test</h2>\n";
        
        $tests = array(
            'test_ajax_parameters',
            'test_module_persistence',
            'test_opportunities_generation',
            'test_ajax_handler_registration'
        );
        
        $passed = 0;
        $total = count($tests);
        
        foreach ($tests as $test) {
            echo "<h3>Running: " . str_replace('_', ' ', ucfirst($test)) . "</h3>\n";
            
            try {
                $result = self::$test();
                if ($result) {
                    echo "<p style='color: green;'>✓ PASSED</p>\n";
                    $passed++;
                } else {
                    echo "<p style='color: red;'>✗ FAILED</p>\n";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>✗ ERROR: " . $e->getMessage() . "</p>\n";
            }
        }
        
        echo "<h3>Test Results: {$passed}/{$total} tests passed</h3>\n";
        
        if ($passed === $total) {
            echo "<p style='color: green; font-weight: bold;'>🎉 All tests passed! The fix should work correctly.</p>\n";
        } else {
            echo "<p style='color: red; font-weight: bold;'>⚠️ Some tests failed. Please review the issues above.</p>\n";
        }
    }
    
    /**
     * Test AJAX parameter compatibility
     */
    private static function test_ajax_parameters() {
        // Simulate the fixed JavaScript parameters
        $fixed_params = array(
            'action' => 'redco_toggle_module',
            'module' => 'page-cache',
            'module_action' => 'enable',
            'nonce' => 'test_nonce'
        );
        
        // Check if the parameters match what the AJAX handler expects
        $required_params = array('module', 'module_action');
        
        foreach ($required_params as $param) {
            if (!isset($fixed_params[$param])) {
                echo "<p>Missing required parameter: {$param}</p>\n";
                return false;
            }
        }
        
        // Check if module_action has correct value
        if ($fixed_params['module_action'] !== 'enable') {
            echo "<p>Incorrect module_action value. Expected 'enable', got: {$fixed_params['module_action']}</p>\n";
            return false;
        }
        
        echo "<p>✓ AJAX parameters are correctly formatted</p>\n";
        return true;
    }
    
    /**
     * Test module persistence
     */
    private static function test_module_persistence() {
        // Get current options
        $options = get_option('redco_optimizer_options', array());
        $original_modules = isset($options['modules_enabled']) ? $options['modules_enabled'] : array();
        
        // Test module: page-cache
        $test_module = 'page-cache';
        
        // Remove test module if it exists
        $test_modules = array_diff($original_modules, array($test_module));
        $options['modules_enabled'] = $test_modules;
        update_option('redco_optimizer_options', $options);
        
        // Verify removal
        $updated_options = get_option('redco_optimizer_options', array());
        $updated_modules = isset($updated_options['modules_enabled']) ? $updated_options['modules_enabled'] : array();
        
        if (in_array($test_module, $updated_modules)) {
            echo "<p>Failed to remove test module</p>\n";
            return false;
        }
        
        // Add test module
        $test_modules[] = $test_module;
        $options['modules_enabled'] = $test_modules;
        update_option('redco_optimizer_options', $options);
        
        // Verify addition
        $final_options = get_option('redco_optimizer_options', array());
        $final_modules = isset($final_options['modules_enabled']) ? $final_options['modules_enabled'] : array();
        
        if (!in_array($test_module, $final_modules)) {
            echo "<p>Failed to add test module</p>\n";
            return false;
        }
        
        // Restore original state
        $options['modules_enabled'] = $original_modules;
        update_option('redco_optimizer_options', $options);
        
        echo "<p>✓ Module persistence works correctly</p>\n";
        return true;
    }
    
    /**
     * Test opportunities generation
     */
    private static function test_opportunities_generation() {
        // Check if the Admin UI class exists and has the required method
        if (!class_exists('Redco_Optimizer_Admin_UI')) {
            echo "<p>Redco_Optimizer_Admin_UI class not found</p>\n";
            return false;
        }
        
        // Check if the get_optimization_opportunities method exists
        $reflection = new ReflectionClass('Redco_Optimizer_Admin_UI');
        if (!$reflection->hasMethod('get_optimization_opportunities')) {
            echo "<p>get_optimization_opportunities method not found</p>\n";
            return false;
        }
        
        // Check if the method is accessible (private methods can still be tested for existence)
        $method = $reflection->getMethod('get_optimization_opportunities');
        if (!$method) {
            echo "<p>get_optimization_opportunities method not accessible</p>\n";
            return false;
        }
        
        echo "<p>✓ Opportunities generation method exists</p>\n";
        return true;
    }
    
    /**
     * Test AJAX handler registration
     */
    private static function test_ajax_handler_registration() {
        global $wp_filter;
        
        // Check if the AJAX action is registered
        $ajax_action = 'wp_ajax_redco_toggle_module';
        
        if (!isset($wp_filter[$ajax_action])) {
            echo "<p>AJAX action {$ajax_action} not registered</p>\n";
            return false;
        }
        
        // Check if our new AJAX action for opportunities is registered
        $opportunities_action = 'wp_ajax_redco_get_optimization_opportunities';
        
        if (!isset($wp_filter[$opportunities_action])) {
            echo "<p>AJAX action {$opportunities_action} not registered</p>\n";
            return false;
        }
        
        echo "<p>✓ AJAX handlers are properly registered</p>\n";
        return true;
    }
}

// Run tests if this file is accessed directly in admin context
if (is_admin() && current_user_can('manage_options')) {
    add_action('admin_init', function() {
        if (isset($_GET['run_redco_test']) && $_GET['run_redco_test'] === '1') {
            Redco_Optimization_Opportunities_Test::run_tests();
            exit;
        }
    });
}
