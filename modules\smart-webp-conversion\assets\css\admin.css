/**
 * Smart WebP Conversion Admin Styles
 *
 * @package Redco_Optimizer
 * @subpackage Smart_WebP_Conversion
 * @since 1.0.0
 */

/* FIXED: Highlight flash effect for toast notification "View Details" action */
.highlight-flash {
    animation: highlightFlash 2s ease-in-out;
    border: 2px solid #4CAF50 !important;
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.3) !important;
}

@keyframes highlightFlash {
    0% {
        background-color: rgba(76, 175, 80, 0.1);
        /* transform removed */
    }
    50% {
        background-color: rgba(76, 175, 80, 0.2);
        /* transform removed */
    }
    100% {
        background-color: transparent;
        /* transform removed */
    }
}

/* Quality Slider Styling */
.quality-slider-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.quality-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #e0e0e0;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.quality-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--redco-primary);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.quality-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--redco-primary);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.quality-value {
    font-weight: 600;
    color: var(--redco-primary);
    min-width: 45px;
    text-align: center;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

/* Conversion History Table */
.conversion-history-table {
    margin-top: 20px;
}

.conversion-history-table table {
    border-collapse: collapse;
}

.conversion-history-table th,
.conversion-history-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.conversion-history-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.conversion-type {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.conversion-type-upload {
    background: #e3f2fd;
    color: #1976d2;
}

.conversion-type-bulk {
    background: #f3e5f5;
    color: #7b1fa2;
}

.conversion-type-manual {
    background: #e8f5e8;
    color: #388e3c;
}

.savings-positive {
    color: #28a745;
    font-weight: 600;
}

/* Progress Modal Styles - Enhanced Centering */
.redco-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 9999999;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.redco-modal-content {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 100%;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
    position: relative;
    margin: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.redco-modal-header {
    padding: 20px 20px 0;
    border-bottom: 1px solid #e0e0e0;
}

.redco-modal-header h3 {
    margin: 0 0 15px 0;
    font-size: 1.2em;
    color: #333;
}

.redco-modal-body {
    padding: 20px;
}

.redco-modal-footer {
    padding: 0 20px 20px;
    text-align: right;
}

/* Modal Close Button */
.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    float: right;
    margin-top: -5px;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #333;
}

/* Test Results Styles */
.test-results {
    margin-bottom: 20px;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child {
    border-bottom: none;
}

.result-label {
    font-weight: 600;
    color: #495057;
}

.result-value {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.result-value.supported {
    background: #d4edda;
    color: #155724;
}

.result-value.unsupported {
    background: #f8d7da;
    color: #721c24;
}

.server-recommendation {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007cba;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.server-recommendation .dashicons {
    font-size: 20px;
    margin-top: 2px;
    flex-shrink: 0;
}

.server-recommendation strong {
    color: #495057;
}

/* WebP Test Modal Specific Styles */
.webp-test-modal {
    min-width: 400px;
}

/* Ensure modal is always centered */
.redco-modal.show {
    display: flex !important;
    opacity: 1;
}

/* Modal content animation when shown */
.redco-modal.show .redco-modal-content {
    transform: scale(1);
}

/* Progress Bar */
.progress-container {
    margin-bottom: 25px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-stats {
    text-align: center;
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 20px;
}

/* Conversion Stats */
.conversion-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.conversion-stats .stat-item {
    text-align: center;
}

.conversion-stats .stat-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
}

.conversion-stats .stat-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

/* Current Operation */
.current-operation {
    margin-bottom: 20px;
    padding: 15px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
}

.current-file {
    font-size: 14px;
    color: #856404;
    font-weight: 500;
}

/* Conversion Log */
.conversion-log {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 6px;
}

.log-header {
    padding: 10px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    font-size: 13px;
    color: #495057;
}

.log-entries {
    padding: 10px;
}

.log-entry {
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
    font-size: 13px;
    display: flex;
    align-items: center;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-entry .dashicons {
    margin-right: 8px;
    font-size: 16px;
}

.log-success {
    color: #28a745;
}

.log-error {
    color: #dc3545;
}

.progress-text {
    text-align: center;
    font-weight: 600;
    color: #495057;
}

/* Conversion Details */
.conversion-details {
    margin-bottom: 25px;
}

.current-file {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid var(--redco-primary);
    margin-bottom: 15px;
    font-weight: 500;
    color: #495057;
}

.conversion-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.conversion-stats .stat {
    text-align: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.conversion-stats .label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.conversion-stats .value {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: var(--redco-primary);
}

/* Conversion Log */
.conversion-log {
    border-top: 1px solid #e9ecef;
    padding-top: 20px;
}

.conversion-log h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    color: #495057;
}

.log-entries {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: #f8f9fa;
}

.log-entry {
    padding: 8px 12px;
    border-bottom: 1px solid #e9ecef;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-entry.log-success {
    color: #28a745;
    background-color: #f8fff8;
}

.log-entry.log-error {
    color: #dc3545;
    background-color: #fff8f8;
}

.log-entry.log-info {
    color: #2196F3;
    background-color: #f8fbff;
}

.log-entry.log-complete {
    color: #28a745;
    background-color: #f0f8f0;
    font-weight: 600;
    border-left: 4px solid #28a745;
}

.log-entry.log-debug {
    color: #646970;
    background-color: #f6f7f7;
    font-size: 12px;
    font-family: monospace;
}

.log-entry .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Compatibility Status */
.compatibility-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.compatibility-item:last-child {
    border-bottom: none;
}

.compatibility-label {
    font-weight: 500;
    color: #495057;
}

.compatibility-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-supported {
    background: #d4edda;
    color: #155724;
}

.status-unsupported {
    background: #f8d7da;
    color: #721c24;
}

/* Alert Styles */
.redco-alert {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
}

.redco-alert-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert-icon {
    flex-shrink: 0;
}

.alert-icon .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
}

.alert-content h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
}

.alert-content p {
    margin: 0;
    line-height: 1.5;
}

/* Empty State */
.redco-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state-icon {
    margin-bottom: 20px;
}

.empty-state-icon .dashicons {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #adb5bd;
}

.redco-empty-state h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: #495057;
}

.redco-empty-state p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

/* Help Links */
.help-links {
    margin-top: 15px;
}

.help-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    color: var(--redco-primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.help-link:hover {
    color: var(--redco-primary-dark);
}

.help-link .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .redco-modal {
        padding: 10px;
    }

    .redco-modal-content {
        width: 100%;
        max-width: none;
        max-height: calc(100vh - 20px);
        margin: 0;
    }

    .conversion-stats {
        grid-template-columns: 1fr;
    }

    .quality-slider-container {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .quality-value {
        text-align: left;
    }

    .result-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .server-recommendation {
        flex-direction: column;
        gap: 8px;
    }

    .webp-test-modal {
        min-width: auto;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .redco-modal {
        padding: 5px;
    }

    .redco-modal-content {
        max-height: calc(100vh - 10px);
    }

    .redco-modal-header,
    .redco-modal-body,
    .redco-modal-footer {
        padding: 15px;
    }
}

/* Debug Log Styling */
.log-entry.log-debug {
    color: #646970;
    background-color: #f6f7f7;
    border-left: 4px solid #646970;
    font-size: 12px;
    font-family: monospace;
}

/* REAL-TIME STATS: Visual feedback for updating statistics */
.stat-value.updating {
    opacity: 0.6;
    position: relative;
}

/* WebP Backup Notice Styling */
.webp-backup-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    margin-top: 12px;
    background: #e8f5e9;
    border: 1px solid #c8e6c9;
    border-radius: 6px;
    font-size: 13px;
    color: #2e7d32;
    line-height: 1.4;
}

.webp-backup-notice .dashicons {
    color: #4caf50;
    font-size: 16px;
    flex-shrink: 0;
}

.webp-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    font-size: 13px;
    color: #1565c0;
    line-height: 1.4;
}

.webp-notice .dashicons {
    color: #2196f3;
    font-size: 16px;
    flex-shrink: 0;
}

.stat-value.updating::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -20px;
    width: 12px;
    height: 12px;
    border: 2px solid #4CAF50;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translateY(-50%);
}

.stat-value.updated {
    background-color: #e8f5e8;
    transition: background-color 0.3s ease;
    border-radius: 3px;
    padding: 2px 4px;
    margin: -2px -4px;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}
