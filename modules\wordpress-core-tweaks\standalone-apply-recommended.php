<?php
/**
 * Standalone Apply Recommended <PERSON><PERSON> for WordPress Core Tweaks
 * 
 * This file provides a minimal, memory-efficient implementation of the
 * "Apply Recommended" functionality without loading the full WordPress Core Tweaks class.
 * 
 * @package Redco_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register the standalone AJAX handler
 * This runs early and doesn't require class instantiation
 */
add_action('wp_ajax_redco_apply_recommended_tweaks', 'redco_minimal_apply_recommended_tweaks');

/**
 * Minimal Apply Recommended WordPress Core Tweaks Handler
 * 
 * This function is completely independent and memory-efficient.
 * It applies basic WordPress Core optimizations without complex validation or processing.
 */
function redco_minimal_apply_recommended_tweaks() {
    // Early memory check - abort if memory usage is already high
    $memory_usage = memory_get_usage(true);
    $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
    
    if ($memory_limit > 0 && $memory_usage > ($memory_limit * 0.6)) {
        header('Content-Type: application/json');
        echo json_encode(array(
            'success' => false,
            'data' => array(
                'message' => 'Server memory usage is too high. Please try again later.',
                'code' => 'MEMORY_HIGH'
            )
        ));
        exit;
    }

    // Security check
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
        header('Content-Type: application/json');
        echo json_encode(array(
            'success' => false,
            'data' => array(
                'message' => 'Security check failed. Please refresh the page and try again.',
                'code' => 'SECURITY_FAILED'
            )
        ));
        exit;
    }

    if (!current_user_can('manage_options')) {
        header('Content-Type: application/json');
        echo json_encode(array(
            'success' => false,
            'data' => array(
                'message' => 'Insufficient permissions.',
                'code' => 'PERMISSION_DENIED'
            )
        ));
        exit;
    }

    // Define recommended WordPress Core optimizations
    $recommended_settings = array(
        'emoji_remove_frontend' => true,
        'emoji_remove_feeds' => true,
        'emoji_remove_emails' => true,
        'remove_wp_version' => true,
        'remove_css_versions' => true,
        'remove_js_versions' => true,
        'limit_revisions' => true,
        'max_revisions' => 5,
        'disable_xmlrpc' => true,
        'disable_pingbacks' => true,
        'remove_shortlink' => true
    );

    // Get current settings (minimal operation)
    $option_name = 'redco_optimizer_wordpress_core_tweaks';
    $current_settings = get_option($option_name, array());
    
    // Ensure current_settings is an array
    if (!is_array($current_settings)) {
        $current_settings = array();
    }

    // Count how many settings will actually change
    $changes_count = 0;
    foreach ($recommended_settings as $key => $value) {
        if (!isset($current_settings[$key]) || $current_settings[$key] !== $value) {
            $changes_count++;
        }
    }

    // Merge recommended settings with current settings
    $updated_settings = array_merge($current_settings, $recommended_settings);

    // Save the updated settings
    $save_result = update_option($option_name, $updated_settings);

    // Simple cache clearing
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
    }

    // Prepare response
    $success = ($save_result || $changes_count === 0);
    
    header('Content-Type: application/json');
    
    if ($success) {
        echo json_encode(array(
            'success' => true,
            'data' => array(
                'message' => $changes_count > 0 
                    ? "Successfully applied {$changes_count} WordPress Core optimizations!" 
                    : 'Your WordPress Core settings are already optimized!',
                'total_optimizations' => $changes_count,
                'code' => 'SUCCESS'
            )
        ));
    } else {
        echo json_encode(array(
            'success' => false,
            'data' => array(
                'message' => 'Failed to save settings. Please try again.',
                'code' => 'SAVE_FAILED'
            )
        ));
    }
    
    exit;
}

/**
 * Check if WordPress Core Tweaks module should be loaded
 * This prevents unnecessary class loading when memory is high
 */
function redco_should_load_wordpress_core_tweaks_class() {
    // Memory check
    $memory_usage = memory_get_usage(true);
    $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
    
    if ($memory_limit > 0 && $memory_usage > ($memory_limit * 0.5)) {
        return false;
    }
    
    // Only load if module is actually enabled
    $options = get_option('redco_optimizer_options', array());
    $enabled_modules = isset($options['modules_enabled']) ? $options['modules_enabled'] : array();
    
    return is_array($enabled_modules) && in_array('wordpress-core-tweaks', $enabled_modules);
}
